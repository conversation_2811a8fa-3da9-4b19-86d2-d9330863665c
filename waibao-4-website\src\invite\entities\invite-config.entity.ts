import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';

@Entity('invite_config')
export class InviteConfig {
  @ApiProperty({ description: '配置ID' })
  @PrimaryGeneratedColumn()
  id: number;

  @ApiProperty({ 
    description: '奖励类型',
    enum: ['REGISTER', 'TASK_PARTICIPATE', 'TASK_CREATE', 'TASK_COMMISSION'],
    example: 'REGISTER'
  })
  @Column({
    comment: '奖励类型',
    type: 'enum',
    enum: ['REGISTER', 'TASK_PARTICIPATE', 'TASK_CREATE', 'TASK_COMMISSION']
  })
  rewardType: string;

  @ApiProperty({ 
    description: '基础奖励积分',
    example: 10
  })
  @Column({
    comment: '基础奖励积分',
    type: 'int'
  })
  basePoints: number;

  @ApiProperty({ 
    description: '阶梯奖励条件(邀请人数)',
    example: 50,
    required: false
  })
  @Column({
    comment: '阶梯奖励条件(邀请人数)',
    type: 'int',
    nullable: true
  })
  tierCondition: number;

  @ApiProperty({ 
    description: '阶梯奖励积分',
    example: 15,
    required: false
  })
  @Column({
    comment: '阶梯奖励积分',
    type: 'int',
    nullable: true
  })
  tierPoints: number;

  @ApiProperty({ 
    description: '每日限制次数',
    example: 10
  })
  @Column({
    comment: '每日限制次数',
    type: 'int'
  })
  dailyLimit: number;

  @ApiProperty({ 
    description: '是否启用',
    default: true
  })
  @Column({
    comment: '是否启用',
    default: true
  })
  enabled: boolean;
} 