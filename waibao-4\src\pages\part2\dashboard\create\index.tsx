import { Input, View, Text, Picker, Textarea } from "@tarojs/components";
import useGetLanguage from "@/pages/hook/useGetLanguage";
import "./index.scss";
import { useRef, useState } from "react";
import Taro from "@tarojs/taro";

export default function Index({ token }) {
  let language = useGetLanguage("Home");
  let title = useRef(null);
  let address = useRef(null);
  let [info, setInfo] = useState({
    title: "",
    address: "",
    startTime: "",
    endTime: "",
    content: "",
  });

  return (
    <View className="min-h-screen bg-gray-50">
      {/* 顶部标题栏 */}
      <View className="bg-white px-5 py-4 shadow-sm mb-4">
        <View className="text-xl font-bold text-gray-800 flex items-center">
          <View className="i-ic-outline-library-books mr-2 text-[#1659c0]" />
          {language["Create task"]}
        </View>
      </View>

      {/* 表单内容 */}
      <View className="bg-white rounded-lg mx-4 p-5 shadow-sm">
        {/* 活动名称 */}
        <View className="mb-4">
          <Text className="block text-gray-700 text-sm font-medium mb-2">
            {language["Activity name"]}
          </Text>
          <Input
            type="text"
            className="w-full px-4 py-2 rounded-lg border-2 border-gray-200 focus:border-[#1659c0] focus:outline-none transition-colors"
            ref={title}
            placeholder={language["Please enter the activity name"]}
          />
        </View>

        {/* 活动地址 */}
        <View className="mb-4">
          <Text className="block text-gray-700 text-sm font-medium mb-2">
            {language["Activity address"]}
          </Text>
          <Input
            ref={address}
            type="text"
            className="w-full px-4 py-2 rounded-lg border-2 border-gray-200 focus:border-[#1659c0] focus:outline-none transition-colors"
            placeholder={language["Please enter the activity address"]}
          />
        </View>

        {/* 活动内容 */}
        <View className="mb-4">
          <Text className="block text-gray-700 text-sm font-medium mb-2">
            {language["Activity content"]}
          </Text>
          <Textarea
            className="w-full px-4 py-2 rounded-lg border-2 border-gray-200 focus:border-[#1659c0] focus:outline-none transition-colors min-h-[120px]"
            value={info.content}
            maxlength={-1}
            onInput={e => {
              setInfo({
                ...info,
                content: e.detail.value
              });
            }}
            placeholder={language["Please enter the activity content"]}
          />
        </View>

        {/* 时间选择 */}
        <View className="mb-4">
          <Text className="block text-gray-700 text-sm font-medium mb-2">
            {language["Start time"]}
          </Text>
          <Picker
            mode="date"
            className="w-full"
            value={info.startTime}
            onChange={(e) => {
              setInfo({
                ...info,
                startTime: e.detail.value,
              });
            }}
          >
            <View className="w-full px-4 py-2 rounded-lg border-2 border-gray-200 text-gray-700">
              {info.startTime || language["Please enter the start time"]}
            </View>
          </Picker>
        </View>

        <View className="mb-6">
          <Text className="block text-gray-700 text-sm font-medium mb-2">
            {language["End time"]}
          </Text>
          <Picker
            mode="date"
            className="w-full"
            value={info.endTime}
            onChange={(e) => {
              setInfo({
                ...info,
                endTime: e.detail.value,
              });
            }}
          >
            <View className="w-full px-4 py-2 rounded-lg border-2 border-gray-200 text-gray-700">
              {info.endTime || language["Please enter the end time"]}
            </View>
          </Picker>
        </View>
      </View>

      {/* 提交按钮 */}
      <View className="px-4 mt-6">
        <View
          className="bg-[#1659c0] text-white text-center py-3 rounded-lg shadow-md active:bg-[#1248a0] transition-colors"
          onClick={() => {
            // 获取表单值
            const titleValue = title.current?.value;
            const addressValue = address.current?.value;
            const { content, startTime, endTime } = info;

            // 验证所有字段
            if (!titleValue?.trim()) {
              return Taro.showToast({
                title: language["Please enter the activity name"],
                icon: "none"
              });
            }

            if (!addressValue?.trim()) {
              return Taro.showToast({
                title: language["Please enter the activity address"],
                icon: "none"
              });
            }

            if (!content?.trim()) {
              return Taro.showToast({
                title: language["Please enter the activity content"],
                icon: "none"
              });
            }

            if (!startTime) {
              return Taro.showToast({
                title: language["Please enter the start time"],
                icon: "none"
              });
            }

            if (!endTime) {
              return Taro.showToast({
                title: language["Please enter the end time"],
                icon: "none"
              });
            }

            // 标题长度验证
            if(titleValue.length > 50) {
              return Taro.showToast({
                title: "标题不能超过50个字",
                icon: "none"
              });
            }

            // 验证开始时间不能大于结束时间
            if (new Date(startTime) >= new Date(endTime)) {
              return Taro.showToast({
                title: "开始时间必须早于结束时间",
                icon: "none"
              });
            }

            Taro.request({
              url: process.env.TARO_APP_HOST + "/api/broadcastroom",
              method: "POST",
              data: {
                ...info,
                title: titleValue,
                address: addressValue,
                show: 0
              },
              header: {
                token,
              },
            }).then((res) => {
              if(res.data.success){
                Taro.showToast({
                  title: "创建成功",
                  icon: "success",
                  duration: 2000
                });
                setTimeout(() => {
                  Taro.redirectTo({
                    url: "/pages/part2/dashboard/index"
                  });
                }, 2000);
              }
            }).catch(err => {
              console.log(err);
              Taro.showToast({
                title: "创建错误！",
                icon: "none",
              });
            })
          }}
        >
          {language["Create"]}
        </View>
      </View>
    </View>
  );
}
