FROM node:18.0-alpine3.14 as build-stage

WORKDIR /app

COPY package.json .

RUN npm config set registry https://registry.npmmirror.com/
RUN npm install -g npm
RUN npm i -g @nestjs/cli
RUN npm install

COPY . .

RUN npm run build

# production stage
FROM node:18.0-alpine3.14 as production-stage

COPY --from=build-stage /app/package.json /app/package.json

WORKDIR /app

RUN npm install --production

COPY --from=build-stage /app/.config /app/.config
COPY --from=build-stage /app/dist/src /app

EXPOSE 3000

CMD ["node", "/app/main.js"]
