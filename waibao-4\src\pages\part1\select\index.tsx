import { Button, Image, View } from "@tarojs/components";
import banner from "@/images/select.jpg";
import Taro from "@tarojs/taro";
import man from '@/images/man.jpg'
import man2 from '@/images/man2.jpg'

import useGetLanguage from "@/pages/hook/useGetLanguage";
import "./index.scss";
import useLogin from "@/pages/hook/useLogin";

export default function Index() {
  let language = useGetLanguage("Select login role");
  useLogin();
  return (
    <View className="full-page">
      <View className="text-center mt-13 ">
        <Image mode="widthFix" src={banner}></Image>
      </View>
      <View className="w-full h-full p-5 box-border bg-gray-100 rounded-2xl">
        <View className=" text-lg font-bold m-b-5 tracking-wider">
          {language["Select login role"]}
        </View>

        <View
          className="px-5 flex justify-between items-center py-3.5 mb-5 rounded-lg bg-white"
          onClick={() => {
            Taro.redirectTo({
              url: "/pages/part3/recommend/index",
            });
          }}
        >
          <View className="flex justify-between items-center">
            {/* <View className=" text-3xl i-emojione-performing-arts"></View> */}
            <Image mode="widthFix" className="rounded-lg w-100 h-100 mx-auto" src={man}></Image>
            <View className="ml-5">
              <View className=" font-bold text-lg">{language["I'm a participant"]}</View>
              <View className=" text-gray-400 text-sm">{language["Participating task"]}</View>
            </View>
          </View>
          <View className="i-ic-outline-arrow-circle-right text-3xl text-gray-3"></View>
        </View>
        <View
          className="px-5 flex justify-between items-center py-3.5 mb-5 rounded-lg bg-white"
          onClick={() => {
            Taro.redirectTo({
              url: "/pages/part2/dashboard/index",
            });
          }}
        >
          <View className="flex justify-between items-center">
            {/* <View className=" text-3xl i-emojione-person-juggling-medium-skin-tone"></View> */}
            <Image mode="widthFix" className="rounded-lg w-100 h-100 mx-auto" src={man2}></Image>
            <View className="ml-5">
              <View className=" font-bold text-lg">{language["I'm the publisher"]}</View>
              <View className=" text-gray-400 text-sm">{language["Publishable activity"]}</View>
            </View>
          </View>
          <View className="i-ic-outline-arrow-circle-right text-3xl text-gray-3"></View>
        </View>
      </View>
    </View>
  );
}
