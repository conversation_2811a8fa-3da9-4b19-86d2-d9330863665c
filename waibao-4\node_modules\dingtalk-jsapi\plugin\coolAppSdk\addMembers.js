"use strict";function addMembers(e){var o;return(0,mobile_1._invoke)("biz.util.callComponent",{componentType:"h5",params:{url:"/im/cool-app-component.html?corpId=".concat(encodeURIComponent(null===(o=null===e||void 0===e?void 0:e.context)||void 0===o?void 0:o.corpId),"#/add-members?params=").concat(encodeURIComponent(JSON.stringify(e))),target:"float",title:"提示",wnId:"addMembers",panelHeight:"percent83"}})}Object.defineProperty(exports,"__esModule",{value:!0}),exports.addMembers=void 0,require("../../entry/union");var mobile_1=require("../../entry/mobile");exports.addMembers=addMembers;