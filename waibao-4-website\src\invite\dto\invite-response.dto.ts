import { ApiProperty } from '@nestjs/swagger';

export class ShareInfoResponse {
  @ApiProperty({ description: '邀请码' })
  inviteCode: string;

  @ApiProperty({ description: '邀请链接' })
  inviteLink: string;

  @ApiProperty({ description: '二维码图片（base64）' })
  qrCode: string;

  @ApiProperty({ description: '分享文本' })
  shareText: string;

  @ApiProperty({
    description: '分享平台配置',
    example: {
      wechat: {
        title: 'xxx平台邀请',
        desc: '注册就送50积分!',
        link: 'https://example.com/register?code=ABC123',
        imgUrl: 'https://example.com/logo.png'
      }
    }
  })
  sharePlatforms: Record<string, any>;
}

export class InviteStatsResponse {
  @ApiProperty({ description: '总邀请人数' })
  totalInvites: number;

  @ApiProperty({ description: '今日邀请人数' })
  todayInvites: number;

  @ApiProperty({ description: '总获得积分' })
  totalPoints: number;

  @ApiProperty({ description: '转化率', example: 65.5 })
  conversionRate: number;

  @ApiProperty({
    description: '每日邀请趋势',
    type: [Object],
    example: [
      { date: '2023-12-14', count: 5 },
      { date: '2023-12-13', count: 3 }
    ]
  })
  dailyTrend: Array<{ date: string; count: number }>;
} 