import { Button, Input, View, Image, Checkbox, Form } from "@tarojs/components";
import Taro from "@tarojs/taro";
import banner from "@/images/register.jpg";
import useGetLanguage from "@/pages/hook/useGetLanguage";
import "./index.scss";
import { useState, useEffect, useRef } from "react";
import { BackButton } from '@/components/BackButton'

export default function Index() {
  let [isread, setisread] = useState(false);
  let [inviterCode, setInviterCode] = useState("");
  let language = useGetLanguage("register and login");

  useEffect(() => {
    // 获取当前环境
    const env = Taro.getEnv();
    
    if (env === 'WEB') {
      // H5环境下，直接从URL获取参数
      const urlParams = new URLSearchParams(window.location.search);
      const code = urlParams.get('code');
      if (code) {
        setInviterCode(code);
      }
    } else {
      // 小程序环境下，从路由参数获取
      const router = Taro.getCurrentInstance().router;
      if (router?.params?.code) {
        setInviterCode(router.params.code);
      }
    }
  }, []);

  const [sendingCode, setSendingCode] = useState(false);
  const [countdown, setCountdown] = useState(0);

  const formRef = useRef<any>(null);

  const [email, setEmail] = useState("");

  const formSubmit = (e) => {
    if (!isread) {
      return Taro.showToast({
        title: "请阅读条款！",
        icon: "none",
        duration: 2000,
      });
    }

    const email = e.detail.value?.email?.toString();
    const phone = e.detail.value?.phone?.toString();
    const password = e.detail.value?.password;
    const verificationCode = e.detail.value?.verificationCode;

    // 表单验证
    if (!email || !phone || !password || !verificationCode) {
      return Taro.showToast({
        title: "请填写完整！",
        icon: "none",
        duration: 2000,
      });
    }

    // 添加密码长度验证
    if (password.length < 6 || password.length > 20) {
      return Taro.showToast({
        title: language["Password length error"] || "密码长度必须在6-20位之间",
        icon: "none",
        duration: 2000,
      });
    }

    // 发送注册请求
    Taro.request({
      url: '/api/user/register',
      method: "POST",
      data: {
        email,
        phone,
        password,
        verificationCode,
        inviterCode: inviterCode,
      },
    })
      .then((res) => {
        if (res.data.success) {
          // 显示审核提示
          Taro.showModal({
            title: '注册成功',
            content: '注册成功，请等待账号管理员审核后登录。审核后将以邮件形式通知您。正在跳转登录界面...',
            showCancel: false,
            success: () => {
              setTimeout(() => {
                Taro.redirectTo({
                  url: "/pages/part1/login/index",
                });
              }, 2000);
            }
          });
        } else {
          // 处理错误响应
          let errorMessage = "注册失败";
          
          if (res.data.data) {
            if (Array.isArray(res.data.data.message)) {
              errorMessage = res.data.data.message.join('\n');
            } else if (typeof res.data.data === 'string') {
              errorMessage = res.data.data;
            } else if (res.data.data.message) {
              errorMessage = res.data.data.message;
            }
          }

          Taro.showModal({
            title: '注册失败',
            content: errorMessage,
            showCancel: false
          });
        }
      })
      .catch((err) => {
        let errorMessage = "注册失败";
        
        if (err.data?.data) {
          if (Array.isArray(err.data.data.message)) {
            errorMessage = err.data.data.message.join('\n');
          } else if (typeof err.data.data === 'string') {
            errorMessage = err.data.data;
          } else if (err.data.data.message) {
            errorMessage = err.data.data.message;
          }
        }

        Taro.showModal({
          title: '注册失败',
          content: errorMessage,
          showCancel: false
        });
      });
  };

  const sendVerificationCode = () => {
    if (!email) {
      return Taro.showToast({
        title: "请输入邮箱",
        icon: "none",
        duration: 2000,
      });
    }

    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return Taro.showToast({
        title: "请输入正确的邮箱格式",
        icon: "none",
        duration: 2000,
      });
    }

    setSendingCode(true);
    Taro.request({
      url: '/api/user/send-verification',
      method: "POST",
      data: { email },
    })
      .then((res) => {
        if (res.statusCode === 201) {
          Taro.showToast({
            title: "验证码已发送",
            icon: "success",
            duration: 2000,
          });
          setCountdown(60);
          const timer = setInterval(() => {
            setCountdown((prev) => {
              if (prev <= 1) {
                clearInterval(timer);
                setSendingCode(false);
                return 0;
              }
              return prev - 1;
            });
          }, 1000);
        } else {
          setSendingCode(false);
          Taro.showToast({
            title: res.data?.message || "发送失败",
            icon: "none",
            duration: 2000,
          });
        }
      })
      .catch((err) => {
        setSendingCode(false);
        Taro.showToast({
          title: err.data?.message || "发送失败",
          icon: "none",
          duration: 2000,
        });
      });
  };

  return (
    <View className='register'>
      <BackButton />
      <Form onSubmit={formSubmit} ref={formRef}>
        <View className="text-center py-2.5">
          <Image mode="widthFix" src={banner}></Image>
        </View>
        <View className="w-full h-full px-10 box-border">
          <View className="text-xl font-bold m-b-5 tracking-wider">
            {language["Mobile login"]}
          </View>
          <View className="flex justify-between items-center w-full">
            <View className="i-ic-baseline-email text-gray-400 text-xl"></View>
            <Input
              type="text"
              name="email"
              value={email}
              onInput={e => setEmail(e.detail.value)}
              className="w-full py-2 px-1 text-sm placeholder-gray-200"
              placeholder={language["Please enter your email"]}
            />
          </View>
          <View className="bg-gray-100 h-0.5 mb-1"></View>
          <View className="flex justify-between items-center w-full">
            <View className="i-ic-baseline-phone-android text-gray-400 text-xl"></View>
            <Input
              type="number"
              name="phone"
              className="w-full py-2 px-1 text-sm placeholder-gray-200"
              placeholder={language["Please enter your phone number"]}
            />
          </View>
          <View className="bg-gray-100 h-0.5 mb-1"></View>
          <View className="flex justify-between items-center w-full">
            <View className="i-ic-baseline-lock text-gray-400 text-xl"></View>
            <Input
              type="password"
              name="password"
              className="w-full py-2 px-1 text-sm placeholder-gray-200"
              placeholder={language["Please enter your password"]}
            />
          </View>
          <View className="bg-gray-100 h-0.5 mb-1"></View>
          <View className="flex justify-between items-center w-full">
            <View className="i-ic-baseline-admin-panel-settings text-gray-400 text-xl"></View>
            <Input
              type="text"
              name="verificationCode"
              className="w-4/6 py-2 px-1 text-sm placeholder-gray-200"
              placeholder={language["Please enter the verification code"]}
            />
            <View 
              className={`w-2/6 text-sm text-center ${sendingCode ? 'text-gray-400' : 'text-[#1659c0]'}`}
              onClick={!sendingCode ? sendVerificationCode : undefined}
            >
              {countdown > 0 ? `${countdown}s` : language["Send verification code"]}
            </View>
          </View>
          <View className="bg-gray-100 h-0.5 mb-1"></View>
          {inviterCode && (
            <View className="flex justify-between items-center w-full mb-4">
              <View className="i-ic-baseline-person-add text-gray-400 text-xl"></View>
              <Input
                type="text"
                value={inviterCode}
                disabled
                className="w-full py-2 px-1 text-sm text-gray-500"
              />
            </View>
          )}
          <Button
            formType="submit"
            className=" bg-[#1659c0] text-white border-none btn"
          >
            {language["Sign in to App"]}
          </Button>
          <View
            className="text-center text-[#1659c0] py-5 text-xs font-bold"
            onClick={() => {
              Taro.redirectTo({
                url: "/pages/part1/login/index",
              });
            }}
          >
            {language["Existing account"]}
          </View>
          <View className=" text-xs text-gray-400 mt-5">
            <View className="ml-1.5 whitespace-nowrap text-center" onClick={() => setisread(!isread)}>
              {isread ? (
                <View className="align-middle i-ic-baseline-check-box  text-lg text-[#1659c0] inline-block mr-1"></View>
              ) : (
                <View className="align-middle i-ic-baseline-check-box-outline-blank text-lg text-[#1659c0] inline-block mr-1"></View>
              )}

              {language["I have read and agree"]}
            </View>
            <View className="flex justify-center items-center mt-2">
              <View
                className=" text-[#1659c0] whitespace-nowrap"
                onClick={() => {
                  Taro.navigateTo({
                    url: "/pages/agreement/user/index",
                  });
                }}
              >
                《{language["User agreement"]}》
              </View>
              <View> {language["and"]}</View>
              <View
                className=" text-[#1659c0] whitespace-nowrap"
                onClick={() => {
                  Taro.navigateTo({
                    url: "/pages/agreement/privacy/index",
                  });
                }}
              >
                《{language["Privacy policy"]}》
              </View>
            </View>
          </View>
        </View>
      </Form>
    </View>
  );
}
