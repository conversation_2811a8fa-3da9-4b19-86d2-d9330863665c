import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between, In, LessThanOrEqual, MoreThanOrEqual } from 'typeorm';
import { InviteRecord } from './entities/invite.entity';
import { User } from '../user/entities/user.entity';
import * as qrcode from 'qrcode';
import { InviteConfig } from './entities/invite-config.entity';
import { RewardConfig } from './entities/reward-config.entity';
import { HttpException } from '@nestjs/common';
import { createHash } from 'crypto';
import { RegisterDto } from '../user/dto/register.dto';

function md5(str: string): string {
  return createHash('md5').update(str).digest('hex');
}

@Injectable()
export class InviteService {
  private readonly logger = new Logger('InviteService');

  constructor(
    @InjectRepository(InviteRecord)
    private inviteRecordRepository: Repository<InviteRecord>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(InviteConfig)
    private configRepository: Repository<InviteConfig>,
    @InjectRepository(RewardConfig)
    private rewardConfigRepository: Repository<RewardConfig>
  ) {}

  private generateInviteCode(): string {
    return Math.random().toString(36).substring(2, 10).toUpperCase();
  }

  async generateQRCode(inviteLink: string): Promise<string> {
    if (!inviteLink) {
      console.error('生成二维码失败：inviteLink 为空');
      return null;
    }

    try {
      const qrOptions = {
        errorCorrectionLevel: 'H' as const,
        type: 'image/png' as const,
        quality: 0.92,
        margin: 1,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        }
      };
      return await qrcode.toDataURL(inviteLink, qrOptions);
    } catch (err) {
      console.error('生成二维码失败：', err);
      return null;
    }
  }

  async getInviteStats(userId: number) {
    const [totalInvites, todayInvites] = await Promise.all([
      this.inviteRecordRepository
        .createQueryBuilder('record')
        .innerJoin('user_table', 'invitee', 'invitee.id = record.inviteeId')
        .where('record.inviterId = :userId', { userId })
        .andWhere('record.rewardType = :rewardType', { rewardType: 'REGISTER' })
        .andWhere('invitee.approved = :approved', { approved: true })
        .getCount(),

      this.inviteRecordRepository
        .createQueryBuilder('record')
        .innerJoin('user_table', 'invitee', 'invitee.id = record.inviteeId')
        .where('record.inviterId = :userId', { userId })
        .andWhere('record.rewardType = :rewardType', { rewardType: 'REGISTER' })
        .andWhere('invitee.approved = :approved', { approved: true })
        .andWhere('record.createdAt BETWEEN :start AND :end', {
          start: new Date(new Date().setHours(0, 0, 0, 0)),
          end: new Date(new Date().setHours(23, 59, 59, 999))
        })
        .getCount()
    ]);

    const totalPoints = await this.inviteRecordRepository
      .createQueryBuilder('record')
      .select('SUM(record.points)', 'total')
      .where('record.inviterId = :userId', { userId })
      .getRawOne();

    return {
      totalInvites,
      todayInvites,
      totalPoints: totalPoints.total || 0
    };
  }

  public async handleInviteReward(inviterId: number, inviteeId: number) {
    try {
      this.logger.log(`handleInviteReward called with inviterId=${inviterId}, inviteeId=${inviteeId}`);
      
      // 检查是否已经存在邀请记录
      const existingRecord = await this.inviteRecordRepository.findOne({
        where: {
          inviterId,
          inviteeId,
          rewardType: 'REGISTER'
        }
      });

      // 如果已经有记录，说明已经处理过这个邀请，直接返回
      if (existingRecord && existingRecord.points > 0) {
        this.logger.log(`Invite reward already processed for inviter=${inviterId}, invitee=${inviteeId}`);
        return;
      }
      
      // 获取最新的用户状态和奖励配置
      const [inviter, invitee, config] = await Promise.all([
        this.userRepository.findOne({
          where: { id: inviterId }
        }),
        this.userRepository.findOne({
          where: { id: inviteeId }
        }),
        this.getRewardConfig()
      ]);

      this.logger.log('Current reward config:', config);

      if (!inviter || !invitee) {
        this.logger.error(`Inviter or invitee not found: ${inviterId}, ${inviteeId}`);
        return;
      }

      // 重新检查用户是否已审核
      if (!invitee.approved) {
        this.logger.error(`Invitee not approved: ${inviteeId}`);
        return;
      }

      this.logger.log(`Processing reward for inviter=${inviter.username}, current stats: daily=${inviter.dailyInviteCount}, total=${inviter.totalInviteCount}, omn=${inviter.omn}`);

      const today = new Date();
      today.setHours(0, 0, 0, 0);

      // 如果是新的一天，重置每日邀请计数
      if (!inviter.lastInviteCountResetDate || new Date(inviter.lastInviteCountResetDate) < today) {
        this.logger.log(`Resetting daily invite count for user ${inviter.id}`);
        inviter.dailyInviteCount = 0;
        inviter.lastInviteCountResetDate = today;
      }

      // 如果已达到每日上限，直接返回
      if (inviter.dailyInviteCount >= config.dailyLimit) {
        this.logger.warn(`Daily invite limit reached for user ${inviter.id}`);
        return;
      }

      // 更新邀请人的统计数据
      inviter.dailyInviteCount = (inviter.dailyInviteCount || 0) + 1;
      inviter.totalInviteCount = (inviter.totalInviteCount || 0) + 1;
      
      // 计算邀请人奖励积分
      let inviterRewardPoints = config.inviterReward;

      // 检查是否达到阶梯奖励条件
      if (config.tierCondition && inviter.totalInviteCount >= config.tierCondition) {
        inviterRewardPoints = config.tierPoints || inviterRewardPoints;
      }

      // 计算被邀请人奖励积分
      const inviteeRewardPoints = config.inviteeReward;

      this.logger.log(`Calculating reward points:`, {
        inviterBaseReward: config.inviterReward,
        inviterTierPoints: config.tierPoints,
        inviterTierCondition: config.tierCondition,
        finalInviterReward: inviterRewardPoints,
        inviteeReward: inviteeRewardPoints
      });

      // 更新邀请人积分
      inviter.omn = (inviter.omn || 0) + inviterRewardPoints;
      
      // 更新被邀请人积分
      invitee.omn = (invitee.omn || 0) + inviteeRewardPoints;

      // 保存更新
      await Promise.all([
        this.userRepository.save(inviter),
        this.userRepository.save(invitee)
      ]);

      // 记录邀请人奖励 - 只记录邀请人获得的奖励
      if (existingRecord) {
        existingRecord.points = inviterRewardPoints;
        await this.inviteRecordRepository.save(existingRecord);
      } else {
        await this.recordInviteReward({
          inviterId: inviter.id,
          inviteeId: inviteeId,
          points: inviterRewardPoints,
          rewardType: 'REGISTER'
        });
      }

      // 记录被邀请人奖励 - 记录到被邀请人名下
      await this.recordInviteReward({
        inviterId: inviteeId,  // 改为被邀请人自己的ID
        inviteeId: inviteeId,
        points: inviteeRewardPoints,
        rewardType: 'REGISTER_BONUS'
      });

      this.logger.log(`Successfully processed invite rewards:`, {
        inviter: {
          id: inviterId,
          reward: inviterRewardPoints,
          newOmn: inviter.omn
        },
        invitee: {
          id: inviteeId,
          reward: inviteeRewardPoints,
          newOmn: invitee.omn
        }
      });
    } catch (error) {
      this.logger.error(`Error in handleInviteReward: ${error.message}`, error.stack);
      throw error;
    }
  }

  // 处理任务创建奖励
  async handleTaskCreationReward(userId: number, taskId: number) {
    try {
      const [user, config] = await Promise.all([
        this.userRepository.findOne({
          where: { id: userId },
          select: ['id', 'username', 'omn', 'inviterId']
        }),
        this.getRewardConfig()
      ]);

      if (!user) {
        this.logger.error(`User not found: ${userId}`);
        return;
      }

      // 计算任务创建奖励
      const creatorReward = config.taskCreatorReward;
      
      // 更新任务创建者的积分
      user.omn = (user.omn || 0) + creatorReward;
      await this.userRepository.save(user);

      // 记录任务创建奖励
      await this.recordInviteReward({
        inviterId: user.inviterId,
        inviteeId: userId,
        points: creatorReward,
        rewardType: 'TASK_CREATE'
      });

      // 如果有邀请人，处理分成
      if (user.inviterId) {
        const inviter = await this.userRepository.findOne({
          where: { id: user.inviterId },
          select: ['id', 'username', 'omn']
        });

        if (inviter) {
          // 计算邀请人分成
          const commission = Math.floor(creatorReward * (config.taskCommissionRate / 100));
          
          // 更新邀请人积分
          inviter.omn = (inviter.omn || 0) + commission;
          await this.userRepository.save(inviter);

          // 记录分成奖励
          await this.recordInviteReward({
            inviterId: inviter.id,
            inviteeId: userId,
            points: commission,
            rewardType: 'TASK_COMMISSION'
          });
        }
      }
    } catch (error) {
      this.logger.error(`Error in handleTaskCreationReward: ${error.message}`, error.stack);
      throw error;
    }
  }

  // 处理任务参与奖励
  async handleTaskParticipationReward(userId: number, taskId: number) {
    try {
      const [user, config] = await Promise.all([
        this.userRepository.findOne({
          where: { id: userId },
          select: ['id', 'username', 'omn', 'inviterId']
        }),
        this.getRewardConfig()
      ]);

      if (!user) {
        this.logger.error(`User not found: ${userId}`);
        return;
      }

      // 计算任务参与奖励
      const participantReward = config.taskParticipantReward;
      
      // 更新参与者积分
      user.omn = (user.omn || 0) + participantReward;
      await this.userRepository.save(user);

      // 记录任务参与奖励
      await this.recordInviteReward({
        inviterId: user.inviterId,
        inviteeId: userId,
        points: participantReward,
        rewardType: 'TASK_PARTICIPATE'
      });

      // 如果有邀请人，处理分成
      if (user.inviterId) {
        const inviter = await this.userRepository.findOne({
          where: { id: user.inviterId },
          select: ['id', 'username', 'omn']
        });

        if (inviter) {
          // 计算邀请人分成
          const commission = Math.floor(participantReward * (config.taskCommissionRate / 100));
          
          // 更新邀请人积分
          inviter.omn = (inviter.omn || 0) + commission;
          await this.userRepository.save(inviter);

          // 记录分成奖励
          await this.recordInviteReward({
            inviterId: inviter.id,
            inviteeId: userId,
            points: commission,
            rewardType: 'TASK_COMMISSION'
          });
        }
      }
    } catch (error) {
      this.logger.error(`Error in handleTaskParticipationReward: ${error.message}`, error.stack);
      throw error;
    }
  }

  // 获取奖励配置
  public async getRewardConfig(): Promise<RewardConfig> {
    const config = await this.rewardConfigRepository.findOne({
      where: { id: 1 } // 假设使用ID为1的配置，你可以根据实际情况调整
    });

    if (!config) {
      // 如果没有配置，使用默认值
      return {
        inviterReward: 10,
        inviteeReward: 5,
        taskCreatorReward: 1,
        taskParticipantReward: 1,
        taskCommissionRate: 1,
        tierPoints: 15,
        tierCondition: 50,
        dailyLimit: 10
      } as RewardConfig;
    }

    return config;
  }

  // 更新奖励配置
  async updateRewardConfig(config: Partial<RewardConfig>): Promise<RewardConfig> {
    const existingConfig = await this.getRewardConfig();
    await this.rewardConfigRepository.update(1, config);
    return this.getRewardConfig();
  }

  async handleUserApproved(userId: number): Promise<void> {
    try {
      this.logger.log(`Starting handleUserApproved for user ${userId}`);
      
      const user = await this.userRepository.findOne({
        where: { id: userId },
        relations: ['inviter']  // 加载邀请人信息
      });

      if (!user) {
        this.logger.error(`User not found: ${userId}`);
        return;
      }

      if (!user.inviterId) {
        this.logger.log(`User ${userId} has no inviter`);
        return;
      }

      this.logger.log(`Found user ${user.username} with inviter ${user.inviterId}`);

      // 先更新用户状态为已审核
      user.approved = true;
      const savedUser = await this.userRepository.save(user);
      this.logger.log(`Updated user ${userId} approved status to true`);

      // 查找现有的邀请记录
      let inviteRecord = await this.inviteRecordRepository.findOne({
        where: {
          inviterId: user.inviterId,
          inviteeId: userId,
          rewardType: 'REGISTER'
        }
      });

      // 如果没有邀请记录，才创建新的
      if (!inviteRecord) {
        this.logger.log(`Creating new invite record for inviter=${user.inviterId}, invitee=${userId}`);
        inviteRecord = await this.inviteRecordRepository.save({
          inviterId: user.inviterId,
          inviteeId: userId,
          points: 0,
          rewardType: 'REGISTER'
        });
        this.logger.log(`Created new invite record with ID ${inviteRecord.id}`);
      } else {
        this.logger.log(`Found existing invite record ${inviteRecord.id}`);
      }

      // 处理邀请奖励
      await this.handleInviteReward(user.inviterId, userId);
      
      this.logger.log(`Successfully completed handleUserApproved for user ${userId}`);
    } catch (error) {
      this.logger.error(`Error in handleUserApproved: ${error.message}`, error.stack);
      throw error;
    }
  }

  async register(user: RegisterDto, ip: string, deviceId: string) {
    try {
      this.logger.log(`Starting registration for user ${user.email}`);
      
      // 验证邮箱格式
      if (!this.validateEmail(user.email)) {
        throw new HttpException('邮箱格式不正确', 401);
      }

      // 检查用户是否存在
      const foundUser = await this.userRepository.findOneBy({
        email: user.email,
      });

      if (foundUser) {
        throw new HttpException('该邮箱已注册！', 401);
      }

      // 处理邀请关系
      let inviter = null;
      if (user.inviterCode) {
        this.logger.log(`Looking up inviter with code ${user.inviterCode}`);
        inviter = await this.userRepository.findOne({
          where: { inviteCode: user.inviterCode }
        });
        
        if (!inviter) {
          throw new HttpException('无效的邀请码', 400);
        }

        const isValid = await this.validateInviteCode(user.inviterCode);
        if (!isValid) {
          throw new HttpException('邀请次数已达上限', 400);
        }
        
        this.logger.log(`Found inviter: ${inviter.username} (ID: ${inviter.id})`);
      }

      // 创建新用户
      const newUser = new User();
      newUser.email = user.email;
      newUser.username = user.email; // 使用邮箱作为用户名
      newUser.password = md5(user.password);
      newUser.inviteCode = this.generateInviteCode();
      newUser.ip = ip;
      newUser.deviceId = deviceId;
      newUser.approved = false;

      if (inviter) {
        newUser.inviterId = inviter.id;
      }

      try {
        this.logger.log('Saving new user');
        const savedUser = await this.userRepository.save(newUser);

        // 如果有邀请人，只创建初始记录，不更新计数
        if (inviter) {
          this.logger.log(`Creating initial invite record for inviter=${inviter.id}, invitee=${savedUser.id}`);
          const existingRecord = await this.inviteRecordRepository.findOne({
            where: {
              inviterId: inviter.id,
              inviteeId: savedUser.id,
              rewardType: 'REGISTER'
            }
          });

          if (!existingRecord) {
            await this.inviteRecordRepository.save({
              inviterId: inviter.id,
              inviteeId: savedUser.id,
              points: 0,
              rewardType: 'REGISTER'
            });
          }
        }

        this.logger.log(`Successfully registered user ${savedUser.id}`);
        return '注册成功';
      } catch (e) {
        this.logger.error('用户注册失败', e);
        throw new HttpException('注册失败', 500);
      }
    } catch (error) {
      this.logger.error(`Error in register: ${error.message}`, error.stack);
      throw error;
    }
  }

  async recordInviteReward(data: {
    inviterId: number;
    inviteeId: number;
    points: number;
    rewardType: string;
  }) {
    return this.inviteRecordRepository.save(data);
  }

  async validateInviteCode(inviteCode: string): Promise<boolean> {
    // 检邀请码是否存在
    const inviter = await this.userRepository.findOne({
      where: { inviteCode }
    });

    if (!inviter) return false;

    // 检查今日邀请是否达到上限
    if (inviter.dailyInviteCount >= 10) return false;

    // 检查IP是否频繁注册
    const recentRegistrations = await this.inviteRecordRepository.count({
      where: {
        inviterId: inviter.id,
        createdAt: Between(
          new Date(new Date().setMinutes(new Date().getMinutes() - 5)), // 5分钟内
          new Date()
        )
      }
    });

    if (recentRegistrations >= 3) return false; // 5分钟内最多3个注册

    return true;
  }

  // 添加IP限制检查
  async checkIpLimit(ip: string): Promise<boolean> {
    const recentRegistrations = await this.userRepository.count({
      where: {
        ip: ip,
        createTime: MoreThanOrEqual(new Date(Date.now() - 24 * 60 * 60 * 1000)) // 24小时内
      }
    });

    return recentRegistrations < 5; // 每个IP 24小时内最多5个注册
  }

  // 检查设备指纹
  async checkDeviceLimit(deviceId: string): Promise<boolean> {
    const recentRegistrations = await this.userRepository.count({
      where: {
        deviceId: deviceId,
        createTime: MoreThanOrEqual(new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)) // 7天内
      }
    });

    return recentRegistrations < 3; // 每个设备7天内最多3个注册
  }

  // 检查手机号注册限制
  async checkPhoneLimit(phone: string): Promise<boolean> {
    const user = await this.userRepository.findOne({
      where: { username: phone }
    });

    if (user) return false; // 手机号已注册

    return true;
  }

  async getLeaderboard(type: 'week' | 'month' | 'total' = 'total') {
    const qb = this.inviteRecordRepository
      .createQueryBuilder('record')
      .select('record.inviterId', 'userId')
      .addSelect('COUNT(DISTINCT record.inviteeId)', 'inviteCount')
      .addSelect('SUM(record.points)', 'totalPoints')
      .where('record.inviterId IS NOT NULL')
      .groupBy('record.inviterId')
      .orderBy('inviteCount', 'DESC')
      .addOrderBy('totalPoints', 'DESC')
      .limit(10);

    if (type === 'week') {
      const weekAgo = new Date();
      weekAgo.setDate(weekAgo.getDate() - 7);
      qb.andWhere('record.createdAt >= :weekAgo', { weekAgo });
    } else if (type === 'month') {
      const monthAgo = new Date();
      monthAgo.setMonth(monthAgo.getMonth() - 1);
      qb.andWhere('record.createdAt >= :monthAgo', { monthAgo });
    }

    const rankings = await qb.getRawMany();

    // 获取用户详情
    const userIds = rankings.map(r => r.userId);
    const users = await this.userRepository.findByIds(userIds);
    const userMap = new Map(users.map(u => [u.id, u]));

    return rankings.map(r => ({
      user: {
        id: r.userId,
        username: userMap.get(r.userId)?.username,
        name: userMap.get(r.userId)?.name
      },
      inviteCount: parseInt(r.inviteCount),
      totalPoints: parseFloat(r.totalPoints)
    }));
  }

  async getPlatformAnalytics(startDate?: string, endDate?: string) {
    const qb = this.inviteRecordRepository
      .createQueryBuilder('record')
      .where('record.inviterId IS NOT NULL');

    if (startDate) {
      qb.andWhere('record.createdAt >= :startDate', { startDate: new Date(startDate) });
    }
    if (endDate) {
      qb.andWhere('record.createdAt <= :endDate', { endDate: new Date(endDate) });
    }

    const [
      totalInvites,
      activeInviters,
      totalRewards,
      dailyStats
    ] = await Promise.all([
      qb.getCount(),
      qb.select('DISTINCT record.inviterId').getCount(),
      qb.select('SUM(record.points)', 'total').getRawOne(),
      qb
        .select('DATE(record.createdAt)', 'date')
        .addSelect('COUNT(*)', 'invites')
        .addSelect('SUM(record.points)', 'rewards')
        .groupBy('DATE(record.createdAt)')
        .orderBy('date', 'DESC')
        .limit(30)
        .getRawMany()
    ]);

    return {
      totalInvites,
      activeInviters,
      totalRewards: totalRewards.total || 0,
      dailyStats: dailyStats.map(stat => ({
        date: stat.date,
        invites: parseInt(stat.invites),
        rewards: parseFloat(stat.rewards)
      }))
    };
  }

  // 添加邮箱验证方法
  private validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }
} 