"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.platformConfig=void 0;var ddSdk_1=require("../lib/ddSdk"),env_1=require("../lib/env"),sdk_1=require("../lib/sdk"),eapp_1=require("../lib/bridge/eapp"),webviewInMiniApp_1=require("../lib/bridge/webviewInMiniApp"),h5Harmony_1=require("../lib/bridge/h5Harmony"),h5Event_1=require("../lib/bridge/h5Event"),weexEvent_1=require("../lib/bridge/weexEvent");exports.platformConfig={platform:env_1.ENV_ENUM.harmony,bridgeInit:function(){var e=(0,env_1.getENV)();return e.appType===sdk_1.APP_TYPE.MINI_APP?Promise.resolve(eapp_1.default):e.appType===sdk_1.APP_TYPE.WEBVIEW_IN_MINIAPP?Promise.resolve(webviewInMiniApp_1.default):(0,h5Harmony_1.h5HarmonyBridgeInit)().then(function(){return h5Harmony_1.default})},authMethod:"runtime.permission.requestJsApis",event:{on:function(e,r){var t=(0,env_1.getENV)();switch(t.appType){case sdk_1.APP_TYPE.WEB:case sdk_1.APP_TYPE.WEBVIEW_IN_MINIAPP:(0,h5Event_1.on)(e,r);break;case sdk_1.APP_TYPE.WEEX:(0,weexEvent_1.on)(e,r);break;default:throw new Error("Not support global event in the platfrom: ".concat(t.appType))}},off:function(e,r){var t=(0,env_1.getENV)();switch(t.appType){case sdk_1.APP_TYPE.WEB:case sdk_1.APP_TYPE.WEBVIEW_IN_MINIAPP:(0,h5Event_1.off)(e,r);break;case sdk_1.APP_TYPE.WEEX:(0,weexEvent_1.off)(e,r);break;default:throw new Error("Not support global event in the platfrom: ".concat(t.appType))}}}},ddSdk_1.ddSdk.setPlatform(exports.platformConfig);