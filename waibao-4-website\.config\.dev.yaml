application:
  port: 3001
  mode: "dev"
  key: "F:/react-main/example.com+5-key.pem"
  cert: "F:/react-main/example.com+5.pem"

db:
  mysql:
    type: "mysql"
    host: "127.0.0.1"
    port: 3306
    username: "root"
    password: "example"
    database: "waibao"
    connectorPackage: "mysql2"
    timezone: "+08:00"
    poolSize: 10

email:
  smtp:
    host: "smtp.qq.com"
    port: 465
    secure: true
    user: "<EMAIL>"  # 例如: <EMAIL>
    pass: "uadnoikcaciccdeg"  # 需要在QQ邮箱设置中获取授权码
