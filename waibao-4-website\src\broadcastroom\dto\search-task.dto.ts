import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsNumber, IsDateString } from 'class-validator';
import { Transform, Type } from 'class-transformer';

export class SearchApprovedTaskDto {
  @ApiProperty({ description: '搜索关键词（任务标题）', required: false })
  @IsOptional()
  @IsString()
  keyword?: string;

  @ApiProperty({ description: '活动地址', required: false })
  @IsOptional()
  @IsString()
  address?: string;

  @ApiProperty({ description: '开始时间（筛选起始日期）', required: false })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @ApiProperty({ description: '结束时间（筛选截止日期）', required: false })
  @IsOptional()
  @IsDateString()
  endDate?: string;

  @ApiProperty({ description: '页码', default: 1, required: false })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Transform(({ value }) => parseInt(value, 10))
  page?: number = 1;

  @ApiProperty({ description: '每页数量', default: 10, required: false })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Transform(({ value }) => parseInt(value, 10))
  limit?: number = 10;

  @ApiProperty({ description: '排序字段', default: 'id', required: false })
  @IsOptional()
  @IsString()
  sortBy?: string = 'id';

  @ApiProperty({ description: '排序方向', default: 'DESC', required: false })
  @IsOptional()
  @IsString()
  sortOrder?: 'ASC' | 'DESC' = 'DESC';
} 