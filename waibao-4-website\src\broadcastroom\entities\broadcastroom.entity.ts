import { User } from 'src/user/entities/user.entity';
import {
  Column,
  CreateDateColumn,
  Entity,
  JoinTable,
  ManyToMany,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity('broadcastroom_table')
export class Broadcastroom {
  @PrimaryGeneratedColumn({
    comment: '任务id',
  })
  id: number;

  @Column({
    comment: '任务名称',
  })
  title: string;

  @Column({
    comment: '活动地址',
  })
  address: string;

  @Column({
    comment: '开始时间',
  })
  startTime: string;

  @Column({
    comment: '结束时间',
  })
  endTime: string;

  @Column({
    comment: '电话',
  })
  phone: string;

  @Column({
    comment: '状态',
  })
  status: string;

  @Column({
    comment: '作者',
  })
  author: number;

  @Column({
    comment:'审核状态',
    default: 0
  })
  show: number;

  @Column({
    comment: '任务内容',
    type: 'text',
    nullable: true
  })
  content: string;

  @ManyToMany(() => User, (user) => user.broadcastrooms)
  @JoinTable()
  users: User[];
}
