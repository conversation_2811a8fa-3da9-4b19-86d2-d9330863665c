import { <PERSON>, Post, Body, Get, Param, Res, HttpException, HttpStatus } from '@nestjs/common';
import { EmailService } from './email.service';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';

@Controller('email')
@ApiTags('邮箱服务')
export class EmailController {
  constructor(private readonly emailService: EmailService) {}

  @Post('send-code')
  @ApiOperation({ summary: '发送验证码' })
  @ApiResponse({ status: 200, description: '验证码发送成功' })
  async sendVerificationCode(@Body() body: { email: string }) {
    try {
      return await this.emailService.sendVerificationCode(body.email);
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
} 