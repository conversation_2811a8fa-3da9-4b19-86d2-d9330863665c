import { View, Text, Image } from "@tarojs/components";
import { useState, useEffect } from "react";
import Taro from "@tarojs/taro";
import { AtIcon } from "taro-ui";
import useGetLanguage from "@/pages/hook/useGetLanguage";
import 'taro-ui/dist/style/components/icon.scss';
import '@/app.scss';
import './index.scss';

interface InviteInfo {
  inviteCode: string;
  inviteLink: string;
  qrCode: string | null;
}

interface InviteStats {
  inviteCode: string;
  dailyInviteCount: number;
  totalInviteCount: number;
  stats: {
    totalInvites: number;
    todayInvites: number;
    totalPoints: number;
  };
}

export default function UserCenter() {
  const [inviteInfo, setInviteInfo] = useState<InviteInfo | null>(null);
  const [inviteStats, setInviteStats] = useState<InviteStats>({
    inviteCode: '',
    dailyInviteCount: 0,
    totalInviteCount: 0,
    stats: {
      totalInvites: 0,
      todayInvites: 0,
      totalPoints: 0
    }
  });
  const language = useGetLanguage("Home");

  useEffect(() => {
    Taro.setNavigationBarTitle({
      title: '用户中心'
    });
    
    fetchInviteInfo();
    fetchInviteStats();
  }, []);

  const fetchInviteInfo = () => {
    const token = Taro.getStorageSync("token");
    if (!token) {
      Taro.redirectTo({ url: "/pages/part1/login/index" });
      return;
    }

    Taro.request({
      url: `${process.env.TARO_APP_HOST}/api/user/invite-link`,
      header: { token },
    }).then(res => {
      if (res.statusCode === 200 && res.data.success) {
        setInviteInfo(res.data.data);
      }
    }).catch(err => {
      console.error('获取邀请信息失败:', err);
      Taro.showToast({
        title: '获取邀请信息失败',
        icon: 'none'
      });
    });
  };

  const fetchInviteStats = () => {
    const token = Taro.getStorageSync("token");
    if (!token) {
      Taro.redirectTo({ url: "/pages/part1/login/index" });
      return;
    }

    Taro.request({
      url: `${process.env.TARO_APP_HOST}/api/user/invite-stats`,
      header: { token },
    }).then(res => {
      if (res.statusCode === 200 && res.data.success) {
        setInviteStats(res.data.data);
      }
    }).catch(err => {
      console.error('获取邀请统计失败:', err);
      Taro.showToast({
        title: '获取邀请统计失败',
        icon: 'none'
      });
    });
  };

  const copyInviteLink = () => {
    if (inviteInfo?.inviteLink) {
      Taro.setClipboardData({
        data: inviteInfo.inviteLink,
        success: () => {
          Taro.showToast({
            title: '链接已复制',
            icon: 'success'
          });
        }
      });
    }
  };

  const copyInviteCode = () => {
    if (inviteInfo?.inviteCode) {
      Taro.setClipboardData({
        data: inviteInfo.inviteCode,
        success: () => {
          Taro.showToast({
            title: '邀请码已复制',
            icon: 'success'
          });
        }
      });
    }
  };

  return (
    <View className="user-center">
      {/* 顶部导航栏 */}
      <View className="flex items-center mb-6">
        <View 
          className="flex items-center justify-center w-8 h-8 rounded-full bg-gray-100"
          onClick={() => Taro.navigateBack()}
        >
          <AtIcon value='chevron-left' size='20' color='#1659c0' />
        </View>
        <Text className="text-xl ml-2">用户中心</Text>
      </View>

      {/* 邀请信息卡片 */}
      <View className="card">
        <Text className="text-lg font-bold mb-4">邀请信息</Text>
        {inviteInfo ? (
          <View className="space-y-4">
            <View className="flex justify-between items-center">
              <Text>邀请码：{inviteInfo.inviteCode}</Text>
              <View 
                className="copy-button"
                onClick={copyInviteCode}
              >
                复制
              </View>
            </View>
            <View className="flex justify-between items-center">
              <Text className="flex-1 mr-2 break-all">邀请链接：{inviteInfo.inviteLink}</Text>
              <View 
                className="copy-button"
                onClick={copyInviteLink}
              >
                复制
              </View>
            </View>
            {inviteInfo.qrCode && (
              <View className="flex flex-col items-center">
                <Text className="mb-2">邀请二维码</Text>
                <Image
                  src={inviteInfo.qrCode}
                  className="w-40 h-40"
                  mode="aspectFit"
                />
              </View>
            )}
          </View>
        ) : (
          <View className="flex items-center justify-center py-8">
            <AtIcon value='loading-3' size='24' color='#1659c0' className='at-icon-spin' />
            <Text className="ml-2 text-gray-500">加载中...</Text>
          </View>
        )}
      </View>

      {/* 邀请统计卡片 */}
      <View className="card">
        <Text className="text-lg font-bold mb-4">邀请统计</Text>
        <View className="space-y-4">
          <View className="grid grid-cols-2 gap-4">
            <View className="stats-box">
              <Text className="label">今日邀请</Text>
              <Text className="value">{inviteStats.dailyInviteCount}</Text>
            </View>
            <View className="stats-box">
              <Text className="label">总积分</Text>
              <Text className="value">{inviteStats.stats.totalPoints}</Text>
            </View>
            <View className="stats-box">
              <Text className="label">总邀请数</Text>
              <Text className="value">{inviteStats.stats.totalInvites}</Text>
            </View>
          </View>
        </View>
      </View>
    </View>
  );
} 