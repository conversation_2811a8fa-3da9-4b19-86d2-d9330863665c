"use strict";function getVirtualEnv(){var n={};switch(framework){case constants_1.FRAMEWORK.VUE:var t=weex.config,e=t.env;n.platform=e.platform,constants_1.RUNTIME.WEEX===runtime&&(n.appVersion=e.appVersion,n.appName=e.appName);break;case constants_1.FRAMEWORK.RAX:constants_1.RUNTIME.WEEX===runtime&&(n.platform=navigator.platform,n.appName=navigator.appName,n.appVersion=navigator.appVersion);break;case constants_1.FRAMEWORK.UNKNOWN:constants_1.RUNTIME.WEB===runtime&&(n.platform=constants_1.RUNTIME.WEB),constants_1.RUNTIME.UNKNOWN===runtime&&(n.platform=constants_1.RUNTIME.UNKNOWN)}return n}Object.defineProperty(exports,"__esModule",{value:!0});var whichOneRuntime_1=require("./whichOneRuntime"),environment_1=require("./environment"),constants_1=require("./constants"),_a=(0,whichOneRuntime_1.default)().split("."),runtime=_a[0],framework=_a[1],virtualEnv=getVirtualEnv(),env=(0,environment_1.default)(runtime,framework,virtualEnv);exports.default=env;