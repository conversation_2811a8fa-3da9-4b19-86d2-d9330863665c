import { <PERSON>, Get, Post, Body, Patch, Param, Delete, Request } from '@nestjs/common';
import { ScheduleService } from './schedule.service';
import { CreateScheduleDto } from './dto/create-schedule.dto';
import { UpdateScheduleDto } from './dto/update-schedule.dto';
import { ApiTags } from '@nestjs/swagger';
import { RequireLogin } from 'src/custom-decorator';

@Controller('schedule')
@ApiTags('任务签到详情')
@RequireLogin()
export class ScheduleController {
  constructor(private readonly scheduleService: ScheduleService) {}

  @Post()
  create(@Body() createScheduleDto: CreateScheduleDto,@Request() req: Request) {
    //@ts-ignore
    const userId = req.user.id;
    return this.scheduleService.create(createScheduleDto,userId);
  }


  @Get()
  findAll(@Request() req: Request) {
     //@ts-ignore
    const userId = req.user.id;
    return this.scheduleService.findAll(userId);
  }

  @Get(':id')
  findOne(@Param('id') id: string,@Request() req: Request) {
     //@ts-ignore
     const userId = req.user.id;
    return this.scheduleService.findOne(+id,userId);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updateScheduleDto: UpdateScheduleDto) {
    return this.scheduleService.update(+id, updateScheduleDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.scheduleService.remove(+id);
  }
}
