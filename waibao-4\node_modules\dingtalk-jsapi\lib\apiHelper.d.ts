export declare const addWatchParamsDeal: (params: any) => any;
export declare const addDefaultCorpIdParamsDeal: (params: any) => any;
export declare const genDefaultParamsDealFn: (defaultParams: object) => (params: any) => any;
export declare const forceChangeParamsDealFn: (forceParams: object) => (params: any) => any;
export declare const genBoolResultDealFn: (boolKeyList: string[]) => (params: any) => any;
export declare const genBizStoreParamsDealFn: (params: any) => any;
