"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.h5AndroidbridgeInit=void 0;var h5BridgeReadyPromise,h5AndroidbridgeInit=function(){return h5BridgeReadyPromise||(h5BridgeReadyPromise=new Promise(function(e,i){var n=function(){try{window.WebViewJavascriptBridgeAndroid=window.nuva&&window.nuva.require(),e({})}catch(e){i(e)}};window.nuva&&(void 0===window.nuva.isReady||window.nuva.isReady)?n():(document.addEventListener("runtimeready",function(){n()},!1),document.addEventListener("runtimefailed",function(e){var n=e&&e.detail||{errorCode:"2",errorMessage:"unknown nuvajs bootstrap error"};i(n)},!1))})),h5BridgeReadyPromise};exports.h5AndroidbridgeInit=h5AndroidbridgeInit;var h5AndroidBridge=function(e,i){return h5BridgeReadyPromise||(h5BridgeReadyPromise=(0,exports.h5AndroidbridgeInit)()),h5BridgeReadyPromise.then(function(){return new Promise(function(n,r){var d=e.split("."),o=d.pop()||"",t=d.join("."),a=function(e){"function"==typeof i.onSuccess&&i.onSuccess(e),n(e)},u=function(e){"function"==typeof i.onFail&&i.onFail(e),r(e)};"function"==typeof window.WebViewJavascriptBridgeAndroid&&window.WebViewJavascriptBridgeAndroid(a,u,t,o,i)})})};exports.default=h5AndroidBridge;