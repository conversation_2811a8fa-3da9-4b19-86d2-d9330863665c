import React, { useState, useEffect, useCallback, useRef } from 'react';
import { GoogleMap, LoadScript, Marker, Circle, InfoWindow } from '@react-google-maps/api';
import Taro from '@tarojs/taro';
import { View } from '@tarojs/components';

const center = { lat: 22.5509376, lng: 114.0260869 };
const radius = 100; // 圆形的半径，单位为米

// 高德地图配置
const AMAP_CONFIG = {
  key: '50050710852369b108b403d9a0c81cef',
  securityJsCode: '109d0a860131c46fa3909e4d2eeb52c4',
  version: '2.0'
};

// Google Maps配置
const GOOGLE_CONFIG = {
  apiKey: 'AIzaSyAseDqoI3pdTFq3RVdXVQU2fo2MSABQJ5I',
  libraries: ['geometry']
};

// 地图服务类型
enum MapService {
  GOOGLE = 'google',
  AMAP = 'amap'
}

interface MapComponentProps {
  onLocationCheck?: (canSign: boolean) => void;
  onSignIn?: (locationData: {
    latitude: number;
    longitude: number;
    address: string;
  }) => void;
}

const MapComponent: React.FC<MapComponentProps> = ({ onLocationCheck, onSignIn }) => {
  // 通用状态
  const [position, setPosition] = useState<{lat: number; lng: number} | null>(null);
  const [address, setAddress] = useState('');
  const [content, setContent] = useState('');
  const [currentMapService, setCurrentMapService] = useState<MapService | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [loadingMessage, setLoadingMessage] = useState('正在加载地图服务...');

  // Google Maps 相关状态
  const [googleMap, setGoogleMap] = useState<google.maps.Map | null>(null);
  const [isGoogleMapLoaded, setIsGoogleMapLoaded] = useState(false);
  const [googleLoadFailed, setGoogleLoadFailed] = useState(false);

  // 高德地图相关状态
  const [amapInstance, setAmapInstance] = useState<any>(null);
  const [isAmapLoaded, setIsAmapLoaded] = useState(false);
  const amapContainerRef = useRef<HTMLDivElement>(null);

  // 超时控制
  const loadTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 加载高德地图
  const loadAmapScript = useCallback(() => {
    return new Promise<void>((resolve, reject) => {
      if (window.AMap) {
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.src = `https://webapi.amap.com/maps?v=${AMAP_CONFIG.version}&key=${AMAP_CONFIG.key}&plugin=AMap.Geocoder`;
      script.async = true;

      script.onload = () => {
        if (window.AMap) {
          // 设置安全密钥
          window._AMapSecurityConfig = {
            securityJsCode: AMAP_CONFIG.securityJsCode,
          };
          resolve();
        } else {
          reject(new Error('高德地图加载失败'));
        }
      };

      script.onerror = () => {
        reject(new Error('高德地图脚本加载失败'));
      };

      document.head.appendChild(script);
    });
  }, []);

  // 初始化高德地图
  const initAmap = useCallback(async () => {
    try {
      setLoadingMessage('正在初始化高德地图...');
      await loadAmapScript();

      if (!amapContainerRef.current) {
        throw new Error('地图容器未找到');
      }

      const map = new window.AMap.Map(amapContainerRef.current, {
        zoom: 18,
        center: [center.lng, center.lat], // 高德地图使用 [lng, lat] 格式
        mapStyle: 'amap://styles/normal',
        viewMode: '2D',
        features: ['bg', 'road', 'building', 'point'],
        showLabel: true,
      });

      // 添加圆形范围
      const circle = new window.AMap.Circle({
        center: [center.lng, center.lat],
        radius: radius,
        strokeColor: '#FF0000',
        strokeOpacity: 0.8,
        strokeWeight: 2,
        fillColor: '#FF0000',
        fillOpacity: 0.35,
      });

      map.add(circle);
      setAmapInstance(map);
      setIsAmapLoaded(true);
      setCurrentMapService(MapService.AMAP);
      setIsLoading(false);

      console.log('高德地图初始化成功');
    } catch (error) {
      console.error('高德地图初始化失败:', error);
      setIsLoading(false);
      Taro.showToast({
        title: '地图服务不可用',
        icon: 'none'
      });
    }
  }, []);

  // Google Maps 加载回调
  const onGoogleMapLoad = useCallback((mapInstance: google.maps.Map) => {
    setGoogleMap(mapInstance);
    setIsGoogleMapLoaded(true);
    setCurrentMapService(MapService.GOOGLE);
    setIsLoading(false);
    console.log('Google Maps 加载成功');
  }, []);

  // Google Maps 加载失败处理
  const onGoogleMapLoadError = useCallback(() => {
    console.warn('Google Maps 加载失败，尝试降级到高德地图');
    setGoogleLoadFailed(true);
    setLoadingMessage('Google Maps 不可用，正在切换到高德地图...');
    initAmap();
  }, [initAmap]);

  // 通用距离计算函数
  const calculateDistance = useCallback((location: {lat: number; lng: number}, center: {lat: number; lng: number}) => {
    if (currentMapService === MapService.GOOGLE && window.google?.maps?.geometry) {
      // 使用 Google Maps 计算距离
      return google.maps.geometry.spherical.computeDistanceBetween(
        new google.maps.LatLng(location.lat, location.lng),
        new google.maps.LatLng(center.lat, center.lng)
      );
    } else if (currentMapService === MapService.AMAP && window.AMap) {
      // 使用高德地图计算距离
      const distance = window.AMap.GeometryUtil.distance(
        [location.lng, location.lat],
        [center.lng, center.lat]
      );
      return distance;
    } else {
      // 使用 Haversine 公式作为备用计算方法
      const R = 6371e3; // 地球半径（米）
      const φ1 = location.lat * Math.PI / 180;
      const φ2 = center.lat * Math.PI / 180;
      const Δφ = (center.lat - location.lat) * Math.PI / 180;
      const Δλ = (center.lng - location.lng) * Math.PI / 180;

      const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
                Math.cos(φ1) * Math.cos(φ2) *
                Math.sin(Δλ/2) * Math.sin(Δλ/2);
      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

      return R * c;
    }
  }, [currentMapService]);

  const isLocationInCircle = useCallback((location: {lat: number; lng: number}, center: {lat: number; lng: number}, radius: number) => {
    try {
      const distance = calculateDistance(location, center);
      const isInRange = distance <= radius;
      onLocationCheck?.(isInRange);
      return isInRange;
    } catch (error) {
      console.error('Error calculating distance:', error);
      onLocationCheck?.(false);
      return false;
    }
  }, [calculateDistance, onLocationCheck]);

  // 通用地理编码函数
  const geocodeLocation = useCallback(async (latlng: {lat: number; lng: number}): Promise<string> => {
    return new Promise((resolve, reject) => {
      if (currentMapService === MapService.GOOGLE && window.google?.maps) {
        // 使用 Google Maps 地理编码
        const geocoder = new google.maps.Geocoder();
        geocoder.geocode({ location: latlng }, (results, status) => {
          if (status === 'OK' && results?.[0]) {
            resolve(results[0].formatted_address);
          } else {
            reject(new Error(`Google 地理编码失败: ${status}`));
          }
        });
      } else if (currentMapService === MapService.AMAP && window.AMap) {
        // 使用高德地图地理编码
        const geocoder = new window.AMap.Geocoder({});
        geocoder.getAddress([latlng.lng, latlng.lat], (status: string, result: any) => {
          if (status === 'complete' && result.regeocode) {
            resolve(result.regeocode.formattedAddress);
          } else {
            reject(new Error(`高德地理编码失败: ${status}`));
          }
        });
      } else {
        reject(new Error('地图服务未初始化'));
      }
    });
  }, [currentMapService]);

  const getLocation = useCallback(() => {
    if (!currentMapService) {
      console.error('地图服务未加载');
      return;
    }

    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(showPosition, showError, {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 60000
      });
    } else {
      Taro.showToast({
        title: '您的浏览器不支持地理定位',
        icon: 'none'
      });
    }
  }, [currentMapService]);

  const showPosition = useCallback(async (position: GeolocationPosition) => {
    try {
      const lat = position.coords.latitude;
      const lon = position.coords.longitude;
      const latlng = { lat, lng: lon };

      setPosition(latlng);

      // 根据当前地图服务设置地图中心
      if (currentMapService === MapService.GOOGLE && googleMap) {
        googleMap.setCenter(latlng);
      } else if (currentMapService === MapService.AMAP && amapInstance) {
        amapInstance.setCenter([latlng.lng, latlng.lat]);
      }

      try {
        // 获取地址信息
        const formattedAddress = await geocodeLocation(latlng);
        setAddress(formattedAddress);

        // 检查是否在签到范围内
        const isInRange = isLocationInCircle(latlng, center, radius);
        setContent(isInRange ? '可签到' : '您的位置不在打卡范围内！');

        // 添加用户位置标记
        if (currentMapService === MapService.GOOGLE && googleMap) {
          // Google Maps 标记会通过 JSX 渲染
        } else if (currentMapService === MapService.AMAP && amapInstance) {
          // 清除之前的标记
          amapInstance.clearMap();

          // 重新添加圆形范围
          const circle = new window.AMap.Circle({
            center: [center.lng, center.lat],
            radius: radius,
            strokeColor: '#FF0000',
            strokeOpacity: 0.8,
            strokeWeight: 2,
            fillColor: '#FF0000',
            fillOpacity: 0.35,
          });
          amapInstance.add(circle);

          // 添加用户位置标记
          const marker = new window.AMap.Marker({
            position: [latlng.lng, latlng.lat],
            title: isInRange ? '可签到' : '您的位置不在打卡范围内！'
          });
          amapInstance.add(marker);

          // 添加信息窗口
          const infoWindow = new window.AMap.InfoWindow({
            content: `<div>${isInRange ? '可签到' : '您的位置不在打卡范围内！'}</div>`,
            offset: new window.AMap.Pixel(0, -30)
          });
          infoWindow.open(amapInstance, [latlng.lng, latlng.lat]);
        }

        if (isInRange) {
          onSignIn?.({
            latitude: latlng.lat,
            longitude: latlng.lng,
            address: formattedAddress
          });
        }
      } catch (geocodeError) {
        console.error('地理编码失败:', geocodeError);
        setContent('获取位置信息失败');
        onLocationCheck?.(false);
      }
    } catch (error) {
      console.error('位置处理出错:', error);
      setContent('位置处理出错');
      onLocationCheck?.(false);
    }
  }, [currentMapService, googleMap, amapInstance, geocodeLocation, isLocationInCircle, onSignIn, onLocationCheck]);

  const showError = useCallback((error: GeolocationPositionError) => {
    let message = '获取位置失败';
    switch (error.code) {
      case error.PERMISSION_DENIED:
        message = '用户拒绝了位置请求';
        break;
      case error.POSITION_UNAVAILABLE:
        message = '位置信息不可用';
        break;
      case error.TIMEOUT:
        message = '获取位置超时';
        break;
    }
    Taro.showToast({
      title: message,
      icon: 'none'
    });
    onLocationCheck?.(false);
  }, [onLocationCheck]);

  // 主要的初始化逻辑
  useEffect(() => {
    // 设置超时控制
    loadTimeoutRef.current = setTimeout(() => {
      if (!currentMapService) {
        console.warn('Google Maps 加载超时，尝试降级到高德地图');
        setGoogleLoadFailed(true);
        setLoadingMessage('Google Maps 加载超时，正在切换到高德地图...');
        initAmap();
      }
    }, 10000); // 10秒超时

    return () => {
      if (loadTimeoutRef.current) {
        clearTimeout(loadTimeoutRef.current);
      }
    };
  }, [initAmap]);

  // 当地图服务加载完成后，获取位置
  useEffect(() => {
    if (currentMapService && ((currentMapService === MapService.GOOGLE && isGoogleMapLoaded) ||
                             (currentMapService === MapService.AMAP && isAmapLoaded))) {
      // 清除超时定时器
      if (loadTimeoutRef.current) {
        clearTimeout(loadTimeoutRef.current);
        loadTimeoutRef.current = null;
      }

      // 延迟一下确保地图完全加载
      setTimeout(() => {
        getLocation();
      }, 500);
    }
  }, [currentMapService, isGoogleMapLoaded, isAmapLoaded, getLocation]);

  return (
    <View>
      <View className="mb-2 text-sm text-gray-600">
        {address || '正在获取位置信息...'}
      </View>

      {/* 加载状态提示 */}
      {isLoading && (
        <View className="flex items-center justify-center h-[300px] bg-gray-100 rounded-lg">
          <View className="text-center">
            <View className="mb-2 text-gray-500">{loadingMessage}</View>
            <View className="text-xs text-gray-400">
              {currentMapService === MapService.AMAP ? '使用高德地图服务' : '尝试Google Maps服务'}
            </View>
          </View>
        </View>
      )}

      {/* Google Maps */}
      {!googleLoadFailed && (
        <View style={{ display: currentMapService === MapService.GOOGLE ? 'block' : 'none' }}>
          <LoadScript
            googleMapsApiKey={GOOGLE_CONFIG.apiKey}
            libraries={GOOGLE_CONFIG.libraries as any}
            onLoad={() => {
              console.log('Google Maps script loaded');
            }}
            onError={onGoogleMapLoadError}
          >
            <GoogleMap
              id="google-map"
              mapContainerStyle={{ height: '300px', width: '100%' }}
              zoom={18}
              center={center}
              options={{
                disableDefaultUI: true,
                zoomControl: false,
                scaleControl: false,
                streetViewControl: false,
                rotateControl: false,
                fullscreenControl: false,
                keyboardShortcuts: false,
                mapTypeControl: false,
                scrollwheel: false,
              }}
              onLoad={onGoogleMapLoad}
              onError={onGoogleMapLoadError}
            >
              <Circle
                center={center}
                radius={radius}
                options={{
                  strokeColor: '#FF0000',
                  strokeOpacity: 0.8,
                  strokeWeight: 2,
                  fillColor: '#FF0000',
                  fillOpacity: 0.35,
                }}
              />
              {position && (
                <Marker position={position}>
                  {content && (
                    <InfoWindow position={position}>
                      <div>{content}</div>
                    </InfoWindow>
                  )}
                </Marker>
              )}
            </GoogleMap>
          </LoadScript>
        </View>
      )}

      {/* 高德地图容器 */}
      <View
        style={{
          display: currentMapService === MapService.AMAP ? 'block' : 'none',
          height: '300px',
          width: '100%'
        }}
      >
        <div
          ref={amapContainerRef}
          style={{ height: '100%', width: '100%' }}
        />
      </View>

      {/* 地图服务状态提示 */}
      {!isLoading && (
        <View className="mt-2 text-xs text-gray-400 text-center">
          当前使用: {currentMapService === MapService.GOOGLE ? 'Google Maps' : '高德地图'}
          {googleLoadFailed && currentMapService === MapService.AMAP && ' (Google Maps不可用，已自动切换)'}
        </View>
      )}
    </View>
  );
};

export default MapComponent;
