import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsEmail, IsOptional, Length, Matches } from 'class-validator';

export class RegisterDto {
  @ApiProperty({ 
    description: '邮箱',
    example: '<EMAIL>'
  })
  @IsEmail({}, { message: '请输入有效的邮箱地址' })
  email: string;

  @ApiProperty({ 
    description: '手机号',
    example: '13800138000'
  })
  @IsString()
  @Length(10, 11, { message: '手机号必须是10-11位' })
  @Matches(/^1\d{10}$/, { message: '请输入有效的手机号' })
  phone: string;

  @ApiProperty({ 
    description: '密码',
    example: '123456'
  })
  @IsString()
  @Length(6, 20, { message: '密码长度必须在6-20位之间' })
  password: string;
  
  @ApiProperty({ 
    description: '验证码',
    required: true
  })
  @IsString()
  verificationCode: string;

  @ApiProperty({ 
    description: '邀请码',
    required: false,
    example: 'ABC123XY'
  })
  @IsString()
  @IsOptional()
  inviterCode?: string;
}
