import {
  Column,
  CreateDateColumn,
  Entity,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity('schedule_table')
export class Schedule {
  @PrimaryGeneratedColumn({
    comment: '记录id',
  })
  id: number;

  @Column({
    comment: '任务id',
  })
  broadcastroomId: number;

  @Column({
    comment: '用户id',
  })
  authorId: number;

  @Column({
    comment: '签到类型',
  })
  action: string;

  @Column({
    comment: '地址',
  })
  address: string;

  @Column({
    comment: '日期',
  })
  date: string;

  @Column({
    comment: '时间',
  })
  time: string;
}
