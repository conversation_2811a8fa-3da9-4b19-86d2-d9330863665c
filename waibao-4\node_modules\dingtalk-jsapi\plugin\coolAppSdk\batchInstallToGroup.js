"use strict";function batchInstallCoolApp(e){var o=Object.assign({},e,{isBatchApi:!0});return(0,mobile_1._invoke)("biz.util.callComponent",{componentType:"h5",params:{url:"/resource-picker/".concat(utils_1.isMobile?"mob":"index",".html?scene=addCoolAppToGroup&params=").concat(encodeURIComponent(JSON.stringify(o))),target:utils_1.isMobile?"":"float",title:"选择会话添加应用",wnId:"addCoolAppToGroup",panelHeight:"percent90"}})}Object.defineProperty(exports,"__esModule",{value:!0}),exports.batchInstallCoolApp=void 0,require("../../entry/union");var mobile_1=require("../../entry/mobile"),utils_1=require("./utils");exports.batchInstallCoolApp=batchInstallCoolApp;