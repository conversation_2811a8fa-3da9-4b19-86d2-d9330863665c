"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var ddSdk_1=require("../lib/ddSdk"),env_1=require("../lib/env"),h5Pc_1=require("../lib/bridge/h5Pc"),eapp_1=require("../lib/bridge/eapp"),sdk_1=require("../lib/sdk"),h5PcEvent_1=require("../lib/bridge/h5PcEvent");ddSdk_1.ddSdk.setPlatform({platform:env_1.ENV_ENUM.pc,bridgeInit:function(){switch((0,env_1.getENV)().appType){case sdk_1.APP_TYPE.MINI_APP:return Promise.resolve(eapp_1.default);default:return(0,h5Pc_1.h5PcBridgeInit)().then(function(){return h5Pc_1.default})}},authMethod:"config",authParamsDeal:function(e){var r=Object.assign({},e);return r.url=window.location.href.split("#")[0],r},event:{on:function(e,r){if((0,env_1.getENV)().appType===sdk_1.APP_TYPE.WEB)return(0,h5PcEvent_1.on)(e,r)},off:function(e,r){if((0,env_1.getENV)().appType===sdk_1.APP_TYPE.WEB)return(0,h5PcEvent_1.off)(e,r)}}});