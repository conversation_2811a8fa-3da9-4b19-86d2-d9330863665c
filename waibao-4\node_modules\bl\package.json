{"name": "bl", "version": "4.1.0", "description": "Buffer List: collect buffers and access with a standard readable Buffer interface, streamable too!", "license": "MIT", "main": "bl.js", "scripts": {"lint": "standard *.js test/*.js", "test": "npm run lint && node test/test.js | faucet"}, "repository": {"type": "git", "url": "https://github.com/rvagg/bl.git"}, "homepage": "https://github.com/rvagg/bl", "authors": ["<PERSON> Vagg <<EMAIL>> (https://github.com/rvagg)", "<PERSON> <<EMAIL>> (https://github.com/mcollina)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/jcrugzz)"], "keywords": ["buffer", "buffers", "stream", "awesomesauce"], "dependencies": {"buffer": "^5.5.0", "inherits": "^2.0.4", "readable-stream": "^3.4.0"}, "devDependencies": {"faucet": "~0.0.1", "standard": "^14.3.0", "tape": "^4.11.0"}}