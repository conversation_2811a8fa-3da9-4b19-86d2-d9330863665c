"use strict";function sendMessageToSingleChat(e){var n,o=JSON.stringify(e).length;return(0,mobile_1._invoke)("biz.util.callComponent",{componentType:"h5",params:{url:"/im/cool-app-component.html?corpId=".concat(encodeURIComponent(null===(n=null===e||void 0===e?void 0:e.context)||void 0===n?void 0:n.corpId),"#/send-message-to-single-chat?params=").concat(encodeURIComponent(JSON.stringify({body:e,bodyLengthList:[o]}))),target:"float",title:"提示",wnId:"sendMessageToSingleChat",panelHeight:"percent83"}})}Object.defineProperty(exports,"__esModule",{value:!0}),exports.sendMessageToSingleChat=void 0,require("../../entry/union");var mobile_1=require("../../entry/mobile");exports.sendMessageToSingleChat=sendMessageToSingleChat;