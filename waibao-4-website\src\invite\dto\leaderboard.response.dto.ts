import { ApiProperty } from '@nestjs/swagger';

class LeaderboardUser {
  @ApiProperty({ 
    description: '用户ID',
    example: 1
  })
  id: number;

  @ApiProperty({ 
    description: '用户名',
    example: '13800138000'
  })
  username: string;

  @ApiProperty({ 
    description: '用户昵称',
    example: '张三',
    nullable: true
  })
  name: string;
}

class LeaderboardItem {
  @ApiProperty({ 
    description: '用户信息',
    type: LeaderboardUser,
    example: {
      id: 1,
      username: '13800138000',
      name: '张三'
    }
  })
  user: LeaderboardUser;

  @ApiProperty({ 
    description: '邀请人数',
    example: 50
  })
  inviteCount: number;

  @ApiProperty({ 
    description: '获得积分',
    example: 750
  })
  totalPoints: number;
}

export class LeaderboardResponse {
  @ApiProperty({
    description: '排行榜数据',
    type: [LeaderboardItem],
    example: [
      {
        user: {
          id: 1,
          username: '13800138000',
          name: '张三'
        },
        inviteCount: 50,
        totalPoints: 750
      },
      {
        user: {
          id: 2,
          username: '13800138001',
          name: '李四'
        },
        inviteCount: 45,
        totalPoints: 680
      },
      {
        user: {
          id: 3,
          username: '13800138002',
          name: '王五'
        },
        inviteCount: 40,
        totalPoints: 600
      }
    ]
  })
  data: LeaderboardItem[];

  @ApiProperty({
    description: '总记录数',
    example: 3
  })
  total: number;

  @ApiProperty({
    description: '统计时间范围',
    example: {
      startDate: '2023-12-01',
      endDate: '2023-12-31',
      type: 'month'
    }
  })
  meta: {
    startDate?: string;
    endDate?: string;
    type: 'week' | 'month' | 'total';
  };
} 