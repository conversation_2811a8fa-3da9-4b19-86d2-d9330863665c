{"name": "postcss-pxtransform", "version": "3.6.16", "description": "PostCSS plugin px 转小程序 rpx及h5 rem 单位", "main": "index.js", "keywords": ["postcss", "css", "postcss-plugin", "pxtransform"], "author": "Pines-<PERSON> <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/NervJS/taro.git"}, "bugs": {"url": "https://github.com/NervJS/taro/issues"}, "homepage": "https://github.com/NervJS/taro#readme", "jest": {"testEnvironment": "node", "testEnvironmentOptions": {}}, "devDependencies": {"jest": "^29.3.1", "jest-cli": "^29.3.1", "postcss": "^8.4.18"}, "peerDependencies": {"postcss": "^8.4.18"}, "scripts": {"test": "jest"}}