import { JwtService } from '@nestjs/jwt';
import {
  CanActivate,
  ExecutionContext,
  Inject,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { Request } from 'express';
import { Reflector } from '@nestjs/core';
import { UserService } from './user/user.service';

@Injectable()
export class LoginGuard implements CanActivate {
  @Inject()
  private reflector: Reflector;

  @Inject(JwtService)
  private jwtService: JwtService;

  @Inject(UserService)
  private userservice: UserService;

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const requireLogin = this.reflector.getAllAndOverride('require-login', [
      context.getClass(),
      context.getHandler(),
    ]);

    const requireAdmin = this.reflector.getAllAndOverride('require-admin', [
      context.getClass(),
      context.getHandler(),
    ]);

    if (!requireLogin && !requireAdmin) {
      return true;
    }

    const request = context.switchToHttp().getRequest<Request>();
    const token = request.cookies?.token || request.get('token');

    if (!token) {
      throw new UnauthorizedException('请先登录');
    }

    try {
      const info = this.jwtService.verify(token);
      request.user = info.user;

      if (info.user.id === 0 && info.user.isAdmin === true) {
        return true;
      }

      if (requireAdmin) {
        const user = await this.userservice.findOne(info.user.id);
        if (!user || !user.isAdmin) {
          throw new UnauthorizedException('需要管理员权限');
        }
      }

      return true;
    } catch (e) {
      console.error('Token verification error:', e);
      if (e instanceof UnauthorizedException) {
        throw e;
      }
      throw new UnauthorizedException('登录 token 失效，请重新登录');
    }
  }
}
