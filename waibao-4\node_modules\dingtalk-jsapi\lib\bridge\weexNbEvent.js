"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.off=exports.on=void 0;var weex_1=require("./weex"),globalEvent=(0,weex_1.requireModule)("globalEvent"),weexNbEvent={isInitEvent:!1,eventsMap:{},RequestIDCacheMap:[],dispatchEvent:function(e){if(e){var n={func:e.name,data:e.data,pageId:e.pageId||"",viewId:e.viewId||"",clientId:""};e.clientId&&(n.clientId=e.clientId),weexNbEvent.dispatchData(n)}},addEventListener:function(e,n){weexNbEvent.isInitEvent||(weexNbEvent.isInitEvent=!0,globalEvent.addEventListener("__nb_bridge__",function(e){if(e&&e.message)try{var n=parseJSON(e.message);weexNbEvent.dispatchData(n)}catch(e){console.error("__nb_bridge__ data parse error",e)}})),weexNbEvent.eventsMap[e]||(weexNbEvent.eventsMap[e]=[]),weexNbEvent.eventsMap[e].push(n)},removeEventListener:function(e,n){var t=weexNbEvent.eventsMap[e];if(t)for(var a=t.length-1;a>=0;a--)t[a]===n&&t.splice(a,1)},dispatchData:function(e){console.log("receive push data",e);var n={param:e.param,pageId:e.pageId,viewId:e.viewId,clientId:e.clientId};e&&e.func?(n.eventName=e.func,weexNbEvent.doEventCallback(n)):e&&e.beforeunload?(n.eventName="beforeunload",n.param={},weexNbEvent.doEventCallback(n)):e&&null!=e.requestId?isFunction(weexNbEvent.RequestIDCacheMap[e.requestId])?(weexNbEvent.RequestIDCacheMap[e.requestId](e.param),delete weexNbEvent.RequestIDCacheMap[e.requestId]):console.log("unknown requestId",e):console.error("unknown push data",e)},doEventCallback:function(e){var n=e.eventName;if(n){var t=weexNbEvent.eventsMap[n];if(t&&t.length>0){var a={name:n};isObject(e.param)&&"android"===weex.config.env.platform.toLowerCase()?Object.assign(a,e.param):a.data=e.param,a.pageId=e.pageId,a.viewId=e.viewId,a.clientId=e.clientId,t.map(function(e){if(isFunction(e))try{e(a)}catch(e){console.error(e)}})}}}},on=function(e,n){weexNbEvent.addEventListener(e,n)};exports.on=on;var off=function(e,n){weexNbEvent.removeEventListener(e,n)};exports.off=off;var isObject=function(e){return e&&"object"==typeof e},isFunction=function(e){return"function"==typeof e},parseJSON=function(e){try{e=JSON.parse(e)}catch(e){}return e};