import { ScrollView, View, Image } from "@tarojs/components";
import { AtSegmentedControl } from "taro-ui";
import { useEffect, useState } from "react";
// import cardDatas from "@/mock/cards.json";
import Card from "@/pages/part2/dashboard/card";

import useGetLanguage from "@/pages/hook/useGetLanguage";
import "./index.scss";
import useLogin from "@/pages/hook/useLogin";
import Taro from "@tarojs/taro";
import { getTime } from "@/pages/utils";
import { AtIcon } from "taro-ui";
import 'taro-ui/dist/style/components/icon.scss';

export default function Index() {
  let language = useGetLanguage("Task list");
  let token = useLogin();
  let [start_datas, set_start_datas] = useState([]);
  let [end_datas, set_end_datas] = useState([]);
  function getData() {
    Taro.request({
      url: process.env.TARO_APP_HOST + "/api/schedule",
      method: "GET",
      header: {
        "Content-Type": "application/json",
        token,
      },
    }).then((res) => {
      let data = res.data.data;
      data = data.map((item) => ({
        ...item,
        time: getTime(item).day,
      }));

      set_end_datas(data.filter((item) => item.status == "已结束").reverse());
      set_start_datas(data.filter((item) => item.status == "未结束").reverse());
    });
  }

  useEffect(() => {
    getData();
  }, []);

  let [value, setValue] = useState(0);
  const onChange = (e) => {
    setValue(e);
  };
  return (
    <View className="full-page px-3 box-border page-gradient pt-3">
      <View className="flex items-center mb-6">
        <View 
          className="flex items-center justify-center w-8 h-8 rounded-full bg-gray-100"
          onClick={() => Taro.navigateBack()}
        >
          <AtIcon value='chevron-left' size='20' color='#1659c0' />
        </View>
        <View className="text-xl ml-2 font-bold text-[#1659c0]">{language["Task Center"]}</View>
      </View>
      <AtSegmentedControl
        values={[
          `${language["Ongoing task"]}（${start_datas.length}）`,
          `${language["Completed task"]}（${end_datas.length}）`,
        ]}
        onClick={onChange}
        selectedColor="#4774af"
        current={value}
      />
      {value === 0 ? (
        <View className="tab-content pt-3">
          {start_datas.map((item) => (
            <Card key={item.id} item={item}></Card>
          ))}
        </View>
      ) : null}
      {value === 1 ? (
        <View className="tab-content pt-3" style={{ pointerEvents: "none" }}>
          {end_datas.map((item) => (
            <Card key={item.id} item={item}></Card>
          ))}
        </View>
      ) : null}
    </View>
  );
}
