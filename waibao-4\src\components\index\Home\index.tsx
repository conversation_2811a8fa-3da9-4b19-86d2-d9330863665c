import { Swiper, SwiperItem, View, Image } from "@tarojs/components";

const Card = () => (
  <View className="w-[49%] rounded-lg overflow-hidden bg-white mb-2">
    <Image
      src="https://cdn.shopifycdn.net/s/files/1/0269/0652/5774/products/culturelle-probiotic-prebiotic-gummies-front.png?crop=center&height=600&v=1681738946&width=600"
      className="w-full h-[350px]"
    />
    <View className=" font-bold p-1.5"> Culturelle® Metabolism + Weight Management Capsules</View>
    <View className="font-bold text-red-600 p-1.5"> 183900积分</View>
  </View>
);

export default function Home() {
  return (
    <View className="bg-gray-100">
      <View className=" relative z-0">
        <Image
          src="https://culturelle.com/cdn/shop/files/womens-4in1-homepage.png"
          className="w-full"
        />
      </View>
      <View className="p-2.5 rounded-3xl box-border bg-gray-100 mx-auto -mt-5 text-sm z-10 relative">
        <View className=" bg-white w-11/12 mx-auto rounded-xl p-2.5">
        <View className="flex justify-between items-center">
          <View className=" font-bold text-lg text-red-600">183900积分</View>
          <View className=" rounded-2xl bg-red-600 text-white px-3.5 py-1.5 ">
            立即兑换
          </View>
        </View>
        <View className="text-black mt-3.5">
          积分即将到期，为避免失效，请尽快使用！
        </View>
        </View>
      </View>
      <View className="flex justify-between flex-wrap overflow-hidden pb-50 box-border items-center mx-auto w-11/12 text-sm mt-2.5">
        {new Array(50).fill(0).map((item) => (
          <Card></Card>
        ))}
      </View>
    </View>
  );
}
