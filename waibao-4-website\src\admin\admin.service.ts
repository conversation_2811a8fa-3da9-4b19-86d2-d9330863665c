import { Injectable, UnauthorizedException, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, EntityManager } from 'typeorm';
import { User } from '../user/entities/user.entity';
import { Broadcastroom } from '../broadcastroom/entities/broadcastroom.entity';
import { LoginDto } from './dto/login.dto';
import { JwtService } from '@nestjs/jwt';
import { UserService } from '../user/user.service';
import { Logger } from '@nestjs/common';
import { EmailService } from '../email/email.service';
import { ConfigService } from '@nestjs/config';
import { UserQueryDto } from './dto/user-management.dto';
import { TaskQueryDto } from './dto/task-management.dto';
import { InjectEntityManager } from '@nestjs/typeorm';

@Injectable()
export class AdminService {
  private readonly logger = new Logger(AdminService.name);

  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Broadcastroom)
    private readonly taskRepository: Repository<Broadcastroom>,
    @InjectEntityManager()
    private readonly entityManager: EntityManager,
    private jwtService: JwtService,
    private readonly userService: UserService,
    private readonly emailService: EmailService,
    private readonly configService: ConfigService
  ) {}

  async login(loginDto: LoginDto) {
    if (loginDto.username !== '123' || loginDto.password !== '123') {
      throw new UnauthorizedException('管理员账号或密码错误');
    }

    const adminUser = {
      id: 0,
      username: 'admin',
      isAdmin: true,
      omn: 0
    };

    const token = await this.jwtService.signAsync({
      user: adminUser
    });

    return { token };
  }

  async getPendingUsers() {
    return this.userRepository.find({
      where: { approved: false },
    });
  }

  async approveUser(id: number) {
    try {
      this.logger.log(`Starting approval process for user ${id}`);
      
      const user = await this.userRepository.findOne({
        where: { id },
        select: ['id', 'username', 'approved', 'inviterId', 'email']
      });

      if (!user) {
        throw new NotFoundException(`User ${id} not found`);
      }

      if (user.approved) {
        this.logger.log(`User ${id} is already approved`);
        return { message: '用户已审核通过' };
      }

      this.logger.log(`Approving user ${id} (${user.username}), inviterId: ${user.inviterId}`);
      
      // 使用 userService 的 update 方法来确保触发邀请奖励逻辑
      await this.userService.update(id, { approved: true });
      
      // 发送审核通过邮件
      const emailHtml = `
        <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
          <h2 style="color: #333; text-align: center;">账号审核通过通知</h2>
          <div style="background-color: #f5f5f5; padding: 20px; border-radius: 5px; margin: 20px 0;">
            <p style="font-size: 16px; color: #666;">尊敬的用户：</p>
            <p style="color: #666;">恭喜您，您的账号已通过审核！现在您可以正常使用所有功能了。</p>
            <p style="color: #666;">如有任何问题，请随时联系我们的客服。</p>
          </div>
          <p style="color: #999; font-size: 14px; text-align: center;">此邮件为系统自动发送，请勿回复</p>
        </div>
      `;

      // 使用 EmailService 的公共方法发送邮件
      await this.emailService.sendEmail(
        user.email,
        '账号审核通过通知',
        emailHtml
      );
      
      this.logger.log(`Successfully approved user ${id} and sent notification email`);
      return { message: '用户审核通过' };
    } catch (error) {
      this.logger.error(`Error approving user ${id}: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getPendingTasks() {
    return this.taskRepository.find({
      where: { show: 0 },
    });
  }

  async approveTask(id: number, approved: boolean) {
    await this.taskRepository.update(id, { show: approved ? 1 : -1 });
    return { message: approved ? '任务审核通过' : '任务审核拒绝' };
  }

  // 新增用户管理相关接口
  async getAllUsers(query: UserQueryDto) {
    try {
      const page = query.page || 1;
      const limit = query.limit || 10;
      const skip = (page - 1) * limit;

      const [users, total] = await this.userRepository.findAndCount({
        select: ['id', 'username', 'email', 'phone', 'omn', 'createTime', 'approved', 'totalInviteCount', 'dailyInviteCount'],
        skip,
        take: limit,
        order: { createTime: 'DESC' }
      });

      return {
        data: users,
        meta: {
          total,
          page,
          limit,
          totalPages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      this.logger.error(`Error getting all users: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getUserById(id: number) {
    try {
      const user = await this.userRepository.findOne({
        where: { id },
        select: ['id', 'username', 'email', 'phone', 'omn', 'createTime', 'approved', 'totalInviteCount', 'dailyInviteCount']
      });

      if (!user) {
        throw new NotFoundException(`用户 ID ${id} 不存在`);
      }

      return user;
    } catch (error) {
      this.logger.error(`Error getting user by id ${id}: ${error.message}`, error.stack);
      throw error;
    }
  }

  async deleteUser(id: number) {
    try {
      const user = await this.userRepository.findOne({
        where: { id },
        relations: ['broadcastrooms']
      });

      if (!user) {
        throw new NotFoundException(`用户 ID ${id} 不存在`);
      }

      // 使用事务来确保数据一致性
      return await this.entityManager.transaction(async (manager) => {
        // 1. 清除用户与任务的关联
        if (user.broadcastrooms && user.broadcastrooms.length > 0) {
          user.broadcastrooms = [];
          await manager.save(user);
        }
        
        // 2. 删除用户作为被邀请人的记录
        await manager.query('DELETE FROM invite_records WHERE inviteeId = ?', [id]);
        
        // 3. 删除用户作为邀请人的记录
        await manager.query('DELETE FROM invite_records WHERE inviterId = ?', [id]);
        
        // 4. 最后删除用户
        await manager.remove(user);
        
        return { message: `用户 ID ${id} 已成功删除` };
      });
    } catch (error) {
      this.logger.error(`Error deleting user ${id}: ${error.message}`, error.stack);
      throw error;
    }
  }

  // 新增任务管理相关接口
  async getAllTasks(query: TaskQueryDto) {
    try {
      const page = query.page || 1;
      const limit = query.limit || 10;
      const skip = (page - 1) * limit;

      const [tasks, total] = await this.taskRepository.findAndCount({
        skip,
        take: limit,
        order: { id: 'DESC' }
      });

      return {
        data: tasks.map(task => ({
          ...task,
          approved: task.show === 1
        })),
        meta: {
          total,
          page,
          limit,
          totalPages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      this.logger.error(`Error getting all tasks: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getTaskById(id: number) {
    try {
      const task = await this.taskRepository.findOne({
        where: { id },
        relations: ['users']
      });

      if (!task) {
        throw new NotFoundException(`任务 ID ${id} 不存在`);
      }

      return task;
    } catch (error) {
      this.logger.error(`Error getting task by id ${id}: ${error.message}`, error.stack);
      throw error;
    }
  }
} 