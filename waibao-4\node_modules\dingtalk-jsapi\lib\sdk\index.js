"use strict";function getTargetApiConfigVS(e,o){var i=e&&e.vs;return"object"==typeof i&&(i=i[o.platformSub]),i}Object.defineProperty(exports,"__esModule",{value:!0}),exports.Sdk=exports.LogLevel=exports.APP_TYPE=exports.isFunction=exports.compareVersion=exports.ENV_ENUM_SUB=exports.ENV_ENUM=void 0;var sdkLib_1=require("./sdkLib");Object.defineProperty(exports,"APP_TYPE",{enumerable:!0,get:function(){return sdkLib_1.APP_TYPE}}),Object.defineProperty(exports,"LogLevel",{enumerable:!0,get:function(){return sdkLib_1.LogLevel}}),Object.defineProperty(exports,"isFunction",{enumerable:!0,get:function(){return sdkLib_1.isFunction}}),Object.defineProperty(exports,"compareVersion",{enumerable:!0,get:function(){return sdkLib_1.compareVersion}}),Object.defineProperty(exports,"ENV_ENUM",{enumerable:!0,get:function(){return sdkLib_1.ENV_ENUM}}),Object.defineProperty(exports,"ENV_ENUM_SUB",{enumerable:!0,get:function(){return sdkLib_1.ENV_ENUM_SUB}});var Sdk=function(){function e(e,o){var i=this;this.configJsApiList=[],this.hadConfig=!1,this.p={},this.config$=new Promise(function(e,o){i.p.reject=o,i.p.resolve=e}),this.logQueue=[],this.devConfig={debug:!1},this.platformConfigMap={},this.invokeAPIConfigMapByMethod={},this.isBridgeDrity=!0,this.getExportSdk=function(){return i.exportSdk},this.setAPI=function(e,o){i.invokeAPIConfigMapByMethod[e]=o},this.setPlatform=function(e){i.isBridgeDrity=!0,i.platformConfigMap[e.platform]=e,e.platform===i.env.platform&&e.bridgeInit().catch(function(e){i.customLog(sdkLib_1.LogLevel.WARNING,["auto bridgeInit error",e||""])})},this.getPlatformConfigMap=function(){return i.platformConfigMap},this.deleteApiConfig=function(e,o){var t=i.invokeAPIConfigMapByMethod[e];t&&delete t[o]},this.invokeAPI=function(e,o,t){void 0===o&&(o={}),void 0===t&&(t=!0),i.customLog(sdkLib_1.LogLevel.INFO,['==> "'.concat(e,'" params: '),o]);var n=+new Date,r=n+"_"+Math.floor(1e3*Math.random());if(i.devConfig.onBeforeInvokeAPI)try{i.devConfig.onBeforeInvokeAPI({invokeId:r,method:e,params:o,startTime:n})}catch(e){i.customLog(sdkLib_1.LogLevel.ERROR,["call Hook:onBeforeInvokeAPI failed, reason:",e])}return!1===i.devConfig.isAuthApi&&(t=!1),i.bridgeInitFn().then(function(s){var a=i.invokeAPIConfigMapByMethod[e],f=i.devConfig.forceEnableDealApiFnMap&&i.devConfig.forceEnableDealApiFnMap[e]&&!0===i.devConfig.forceEnableDealApiFnMap[e](o),c=!f&&(!0===i.devConfig.isDisableDeal||i.devConfig.disbaleDealApiWhiteList&&-1!==i.devConfig.disbaleDealApiWhiteList.indexOf(e));if(a||!t){var u;if(a&&(u=a[i.env.platform]),u||!t){var d={};d=!c&&u&&u.paramsDeal&&(0,sdkLib_1.isFunction)(u.paramsDeal)?u.paramsDeal(o):Object.assign({},o);var g=function(e){return!c&&u&&u.resultDeal&&(0,sdkLib_1.isFunction)(u.resultDeal)?u.resultDeal(e):e};if((0,sdkLib_1.isFunction)(d.onSuccess)){var l=d.onSuccess;d.onSuccess=function(e){l(g(e))}}return s(e,d).then(g,function(o){var n=i.hadConfig&&void 0===i.isReady&&-1!==i.configJsApiList.indexOf(e),r="object"==typeof o&&"string"==typeof o.errorCode&&o.errorCode===sdkLib_1.ERROR_CODE.no_permission,a="object"==typeof o&&"string"==typeof o.errorCode&&o.errorCode===sdkLib_1.ERROR_CODE.cancel,f=getTargetApiConfigVS(u,i.env),c=f&&i.env.version&&(0,sdkLib_1.compareVersion)(i.env.version,f),l=(i.env.platform===sdkLib_1.ENV_ENUM.ios||i.env.platform===sdkLib_1.ENV_ENUM.android||i.env.platform===sdkLib_1.ENV_ENUM.harmony)&&n&&r,p=i.env.platform===sdkLib_1.ENV_ENUM.pc&&n&&(c&&!a&&t||r);return l||p?i.config$.then(function(){return s(e,d).then(g)}):Promise.reject(o)}).then(function(t){if(i.devConfig.onAfterInvokeAPI)try{i.devConfig.onAfterInvokeAPI({invokeId:r,method:e,params:o,payload:t,isSuccess:!0,startTime:n,duration:+new Date-n})}catch(e){i.customLog(sdkLib_1.LogLevel.ERROR,["call Hook:onAfterInvokeAPI failed, reason:",e])}return i.customLog(sdkLib_1.LogLevel.INFO,['<== "'.concat(e,'" success result: '),t]),t},function(t){if(i.devConfig.onAfterInvokeAPI)try{i.devConfig.onAfterInvokeAPI({invokeId:r,method:e,params:o,payload:t,startTime:n,duration:+new Date-n,isSuccess:!1})}catch(t){i.customLog(sdkLib_1.LogLevel.ERROR,["call Hook:onAfterInvokeAPI failed, reason:",t])}return i.customLog(sdkLib_1.LogLevel.WARNING,['<== "'.concat(e,'" fail result: '),t]),Promise.reject(t)})}var p='"'.concat(e,'" do not support the current platform (').concat(i.env.platform,")");return i.customLog(sdkLib_1.LogLevel.ERROR,[p]),Promise.reject({errorCode:sdkLib_1.ERROR_CODE.jsapi_internal_error,errorMessage:p})}var p="This API method is not configured for the platform (".concat(i.env.platform,")");return i.customLog(sdkLib_1.LogLevel.ERROR,[p]),Promise.reject({errorCode:sdkLib_1.ERROR_CODE.jsapi_internal_error,errorMessage:p})})},this.isLogQueueTimeout=!1,this.customLog=function(e,o){var t={level:e,text:o,time:new Date};if(!0===i.devConfig.debug)i.customLogInstance(t);else if(!i.isLogQueueTimeout){i.logQueueTimer||(i.logQueueTimer=setTimeout(function(){i.isLogQueueTimeout=!0,i.logQueue=[]},1e4)),i.logQueue.push(t);i.logQueue.length>10&&(i.logQueue=i.logQueue.slice(i.logQueue.length-10))}},this.clearLogQueue=function(){i.logQueue.forEach(function(e){i.customLogInstance(e)}),i.logQueue=[]},this.customLogInstance=o,this.env=e,this.bridgeInitFn=function(){if(i.bridgeInitFnPromise&&!i.isBridgeDrity)return i.bridgeInitFnPromise;i.isBridgeDrity=!1;var o=i.platformConfigMap[e.platform];if(o)i.bridgeInitFnPromise=o.bridgeInit().catch(function(e){return i.customLog(sdkLib_1.LogLevel.ERROR,["\b\b\b\b\bJsBridge initialization fails, jsapi will not work"]),Promise.reject(e)});else{var t="Do not support the current environment：".concat(e.platform);i.customLog(sdkLib_1.LogLevel.WARNING,[t]),i.bridgeInitFnPromise=Promise.reject(new Error(t))}return i.bridgeInitFnPromise};var t=function(e){void 0===e&&(e={}),i.devConfig=Object.assign(i.devConfig,e),!0===e.debug&&i.clearLogQueue(),e.extraPlatform&&i.setPlatform(e.extraPlatform)};this.exportSdk={config:function(o){void 0===o&&(o={});var n=!0;Object.keys(o).forEach(function(e){-1===["debug","usePromise"].indexOf(e)&&(n=!1)}),n?(i.customLog(sdkLib_1.LogLevel.WARNING,["This is a deprecated feature, recommend use dd.devConfig"]),t(o)):i.hadConfig?i.customLog(sdkLib_1.LogLevel.WARNING,["Config has been executed"]):(o.jsApiList&&(i.configJsApiList=o.jsApiList),i.hadConfig=!0,i.bridgeInitFn().then(function(t){var n=i.platformConfigMap[e.platform],r=o;n.authParamsDeal&&(r=n.authParamsDeal(r)),t(n.authMethod,r).then(function(e){i.isReady=!0,i.p.resolve(e)}).catch(function(e){i.isReady=!1,i.p.reject(e)})},function(e){i.customLog(sdkLib_1.LogLevel.ERROR,['\b\b\b\b\bJsBridge initialization failed and "dd.config" failed to call']),i.p.reject(e)}))},devConfig:t,ready:function(e){!1===i.hadConfig?(i.customLog(sdkLib_1.LogLevel.WARNING,["You don 't use a dd.config, so you don't need to wrap dd.ready, recommend remove dd.ready"]),i.bridgeInitFn().then(function(){e()})):i.config$.then(function(o){e()})},error:function(e){i.config$.catch(function(o){e(o)})},on:function(o,t){i.bridgeInitFn().then(function(){i.platformConfigMap[e.platform].event.on(o,t)})},off:function(o,t){i.bridgeInitFn().then(function(){i.platformConfigMap[e.platform].event.off(o,t)})},env:e,checkJsApi:function(o){void 0===o&&(o={});var t={};return o.jsApiList&&o.jsApiList.forEach(function(o){var n=i.invokeAPIConfigMapByMethod[o];if(n){var r=n[e.platform],s=getTargetApiConfigVS(r,e);s&&e.version&&(0,sdkLib_1.compareVersion)(e.version,s)&&(t[o]=!0)}t[o]||(t[o]=!1)}),Promise.resolve(t)},_invoke:function(e,o){return void 0===o&&(o={}),i.invokeAPI(e,o,!1)}}}return e}();exports.Sdk=Sdk;