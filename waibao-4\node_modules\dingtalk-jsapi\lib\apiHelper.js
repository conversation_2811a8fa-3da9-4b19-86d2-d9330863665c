"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.genBizStoreParamsDealFn=exports.genBoolResultDealFn=exports.forceChangeParamsDealFn=exports.genDefaultParamsDealFn=exports.addDefaultCorpIdParamsDeal=exports.addWatchParamsDeal=void 0;var addWatchParamsDeal=function(a){var e=Object.assign({},a);return e.watch=!0,e};exports.addWatchParamsDeal=addWatchParamsDeal;var addDefaultCorpIdParamsDeal=function(a){var e=Object.assign({},a);return e.corpId="corpId",e};exports.addDefaultCorpIdParamsDeal=addDefaultCorpIdParamsDeal;var genDefaultParamsDealFn=function(a){var e=Object.assign({},a);return function(a){return Object.assign({},e,a)}};exports.genDefaultParamsDealFn=genDefaultParamsDealFn;var forceChangeParamsDealFn=function(a){var e=Object.assign({},a);return function(a){return Object.assign(a,e)}};exports.forceChangeParamsDealFn=forceChangeParamsDealFn;var genBoolResultDealFn=function(a){return function(e){var r=Object.assign({},e);return a.forEach(function(a){void 0!==r[a]&&(r[a]=!!r[a])}),r}};exports.genBoolResultDealFn=genBoolResultDealFn;var genBizStoreParamsDealFn=function(a){var e=Object.assign({},a);return"string"!=typeof e.params?(e.params=JSON.stringify(e),e):e};exports.genBizStoreParamsDealFn=genBizStoreParamsDealFn;