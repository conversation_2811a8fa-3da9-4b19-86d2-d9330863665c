"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.batchAppendMockApiResult=exports.appendMockApiResult=exports.emitEvent=exports.init=void 0;var ddSdk_1=require("../lib/ddSdk");require("../lib/polyfills/objectKeys");var mockData={},eventMap={},disableMockApiFilterMap={},init=function(e){var t=ddSdk_1.ddSdk.getExportSdk(),o=ddSdk_1.ddSdk.getPlatformConfigMap()[t.env.platform];t.devConfig({isAuthApi:!1}),ddSdk_1.ddSdk.setPlatform({platform:t.env.platform,authMethod:o&&o.authMethod||"config",bridgeInit:function(){return Promise.resolve(function(t,n){return!mockData[t]||disableMockApiFilterMap[t]&&!1!==disableMockApiFilterMap[t](n)?e&&e.isOnlyMockWhenConfig&&o?o.bridgeInit().then(function(e){return e(t,n)}):Promise.reject({errorMessage:"Not found mock data, current method: ".concat(t),errorCode:"991"}):mockData[t](n).then(function(e){return"function"==typeof n.onSuccess&&n.onSuccess(e),Promise.resolve(e)}).catch(function(e){return"function"==typeof n.onFail&&n.onFail(e),Promise.reject(e)})})},event:{on:function(e,t){eventMap[e]?eventMap[e].push(t):eventMap[e]=[t]},off:function(e,t){var o=eventMap[e];if(o){var n=o.findIndex(function(e){return e===t});-1!==n&&o.splice(n,1)}}}}),e&&e.mockApiMap&&(0,exports.batchAppendMockApiResult)(e.mockApiMap)};exports.init=init;var emitEvent=function(e){ddSdk_1.ddSdk.bridgeInitFn().then(function(){var t=eventMap[e];t&&t.forEach(function(e){e({})})})};exports.emitEvent=emitEvent;var appendMockApiResult=function(e,t,o){var n=ddSdk_1.ddSdk.getExportSdk();if(o&&(disableMockApiFilterMap[e]=o),"function"==typeof t)mockData[e]=t;else{var i=t;mockData[e]=function(e){return i.isSuccess?Promise.resolve(i.payload):Promise.reject(i.payload)}}n.devConfig({disbaleDealApiWhiteList:Object.keys(mockData),forceEnableDealApiFnMap:disableMockApiFilterMap})};exports.appendMockApiResult=appendMockApiResult;var batchAppendMockApiResult=function(e){Object.keys(e).forEach(function(t){(0,exports.appendMockApiResult)(t,e[t])})};exports.batchAppendMockApiResult=batchAppendMockApiResult;