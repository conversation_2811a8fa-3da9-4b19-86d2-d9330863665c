"use strict";function AndroidBridgeCall(e,r,i){(0,weex_1.requireModule)("wxNbBridge").exec({action:e,data:r},function(e){i&&i(e)})}Object.defineProperty(exports,"__esModule",{value:!0}),exports.weexNbBridge=void 0;var weex_1=require("./weex"),useAlipayJSBridge={call:AndroidBridgeCall};"undefined"!=typeof AlipayJSBridge&&(useAlipayJSBridge=AlipayJSBridge);var weexNbBridge=function(e,r){return new Promise(function(i,n){var t=e,d=r.onSuccess,a=r.onFail;delete r.onSuccess,delete r.onFail;var o=t.split("."),c=o.pop()||"",u=o.join(".");useAlipayJSBridge.call("ddExec",{actionName:c,serviceName:u,args:r},function(e){if(!e||!e.success)return a&&a(),n();try{var r=e.content;return r?(r=JSON.parse(e.content),d&&d(r)):d&&d(),i(r)}catch(e){return a&&a(e),n(e)}})})};exports.weexNbBridge=weexNbBridge;