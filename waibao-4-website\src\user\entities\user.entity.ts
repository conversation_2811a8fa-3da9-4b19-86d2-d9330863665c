import { Broadcastroom } from 'src/broadcastroom/entities/broadcastroom.entity';
import {
  Column,
  CreateDateColumn,
  Entity,
  JoinTable,
  ManyToMany,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
  BeforeInsert,
  JoinColumn,
} from 'typeorm';

@Entity({
  name: 'user_table',
})
export class User {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({
    length: 50,
    comment: '用户名',
  })
  username: string;

  @Column({
    length: 50,
    comment: '密码',
  })
  password: string;

  @Column({
    comment: '电话',
    nullable: true,
  })
  phone: string;

  @Column({
    comment: '电子邮箱',
    nullable: true,
  })
  email: string;

  @Column({
    comment: '真实名字',
    nullable: true,
  })
  name: string;

  @CreateDateColumn({
    comment: '创建时间',
  })
  createTime: Date;

  @UpdateDateColumn({
    comment: '更新时间',
  })
  updateTime: Date;

  @Column({
    comment: '用户积分',
    default: 50,
  })
  omn: number;

  @Column({
    comment: '登录ip',
    nullable: true,
  })
  ip: string;

  @Column({
    comment: '是否登出',
    nullable: true,
    default: true,
  })
  loginout: Boolean;

  @Column({
    comment: '微信唯一id',
    nullable: true,
  })
  wxOpenid: string;

  @ManyToMany(() => Broadcastroom, (broadcastroom) => broadcastroom.users)

  broadcastrooms: Broadcastroom[];

  @Column({
    comment: '是否审核通过',
    default: false
  })
  approved: boolean;

  @Column({
    comment: '邀请码',
    unique: true,
    length: 8,
    nullable: false,
  })
  inviteCode: string;

  @Column({
    comment: '上级邀请人ID',
    nullable: true,
  })
  inviterId: number;

  @Column({
    comment: '邀请链接二维码',
    nullable: true,
  })
  qrCode: string;

  @Column({
    comment: '今日邀请人数',
    default: 0,
  })
  dailyInviteCount: number;

  @Column({
    comment: '总邀请人数',
    default: 0,
  })
  totalInviteCount: number;

  @Column({
    comment: '上次重置每日邀请计数的日期',
    type: 'timestamp',
    nullable: true,
  })
  lastInviteCountResetDate: Date;

  @ManyToOne(() => User, user => user.children)
  parent: User;

  @OneToMany(() => User, user => user.parent)
  children: User[];

  @BeforeInsert()
  generateInviteCode() {
    if (!this.inviteCode) {
      this.inviteCode = Math.random().toString(36).substring(2, 10).toUpperCase();
    }
  }

  @Column({
    comment: '是否是管理员',
    default: false,
    type: 'boolean',
    name: 'is_admin'
  })
  isAdmin: boolean;

  @Column({
    comment: '设备ID',
    nullable: true,
    length: 100
  })
  deviceId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'inviterId' })
  inviter: User;
}
