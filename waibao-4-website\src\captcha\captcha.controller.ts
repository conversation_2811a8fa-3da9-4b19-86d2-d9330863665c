import { Controller, Get, Res, Session } from '@nestjs/common';
import { CaptchaService } from './captcha.service';
import * as svgCaptcha from 'svg-captcha';
import { Response } from 'express';
import { getRandomNumber } from 'src/utils';
import { ApiTags } from '@nestjs/swagger';

@Controller('captcha')
@ApiTags('验证码接口')
export class CaptchaController {
  constructor(private readonly captchaService: CaptchaService) {}

  @Get()
  async getCaptcha(@Res() res: Response, @Session() session) {
    const captcha = svgCaptcha.create({
      //create生成普通 createMathExpr生成算术题
      size: 4,
      noise: getRandomNumber(6, 10),
      width: 250,
      // charPreset: 'jwos',
      // background: '#cc9966',
      // color: true,
    });

    session.captcha = captcha.text;
    // console.log(session);
    res.type('svg');
    res.send(captcha.data);
  }
}
