"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.androidWeexBridge=exports.iosWeexBridge=exports.requireModule=void 0;var STATUS_NO_RESULT=0,STATUS_OK=1,STATUS_ERROR=2,WEEX_IOS_BIZ_SUCCESS_CODE="0",requireModule=function(e){return"undefined"!=typeof __weex_require__?__weex_require__("@weex-module/".concat(e)):"undefined"!=typeof weex?weex.requireModule(e):void 0};exports.requireModule=requireModule;var iosWeexBridge=function(){return Promise.resolve(function(e,r){return new Promise(function(o,i){var n=(0,exports.requireModule)("nuvajs-exec"),s=e.split("."),t=s.pop(),u=s.join(".");n.exec({plugin:u,action:t,args:r},function(e){e&&e.errorCode===WEEX_IOS_BIZ_SUCCESS_CODE?("function"==typeof r.onSuccess&&r.onSuccess(e.result),o(e.result)):("function"==typeof r.onFail&&r.onFail(e.result),i(e.result))})})})};exports.iosWeexBridge=iosWeexBridge;var androidWeexBridge=function(){return Promise.resolve(function(e,r){return new Promise(function(o,i){var n=(0,exports.requireModule)("nuvajs-exec"),s=e.split("."),t=s.pop(),u=s.join(".");n.exec({plugin:u,action:t,args:r},function(e){var n={};try{if(e&&e.__message__)if("object"==typeof e.__message__)n=e.__message__;else try{n=JSON.parse(e.__message__)}catch(r){"string"==typeof e.__message__&&(n=e.__message__)}}catch(e){}e&&parseInt(e.__status__+"",10)===STATUS_OK?("function"==typeof r.onSuccess&&r.onSuccess(n),o(n)):("function"==typeof r.onFail&&r.onFail(n),i(n))})})})};exports.androidWeexBridge=androidWeexBridge;