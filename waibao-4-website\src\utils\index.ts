import { parse } from 'yaml';
import * as path from 'path';
import * as fs from 'fs';

// 获取项目运行环境
export const getEnv = () => {
  const env = process.env.RUNNING_ENV || 'prod';
  console.log('当前配置文件运行环境', env);
  return env;
};

// 读取项目配置
export const getConfig = () => {
  const environment = getEnv();
  const yamlPath = path.join(process.cwd(), `./.config/.${environment}.yaml`);
  const file = fs.readFileSync(yamlPath, 'utf8');
  const config = parse(file);
  return config;
};

export const millisecondConversion = (totalDurationInSeconds) => {
  const hours = Math.floor(totalDurationInSeconds / 3600);
  const minutes = Math.floor((totalDurationInSeconds % 3600) / 60);
  const seconds = totalDurationInSeconds % 60;
  return {
    hours,
    minutes,
    seconds,
  };
};

export function getRandomNumber(min, max) {
  // 计算范围内的随机数
  var randomNumber = Math.random() * (max - min) + min;

  // 使用 Math.floor() 函数将浮点数转换为整数
  return Math.floor(randomNumber);
}
