import {  View } from "@tarojs/components";

import useGetLanguage from "@/pages/hook/useGetLanguage";
import "./index.scss";

export default function Index() {
  let language = useGetLanguage("User agreement");
  return (
    <View className="p-5 text-sm">
      {/* <View className=" text-2xl font-bold mb-2 text-center">用户手册</View> */}
      <View className="text-lg font-bold my-1">1. {language["Acceptance clause"]}</View>
      {language["Acceptance clause part1"]}
      <View className="text-lg font-bold my-1">2. {language["Usage license"]}</View>
      {language["Acceptance clause part2"]}
      <View className="text-lg font-bold my-1">3. {language["User behavior"]}</View>
      {language["Acceptance clause part3"]}
      <View className="text-lg font-bold my-1">4. {language["Intellectual property"]}</View>
      {language["Acceptance clause part4"]}
    </View>
  );
}
