import { Button, Input, View, Image, Form } from "@tarojs/components";
import Taro from "@tarojs/taro";
import banner from "@/images/login.jpg";
import icon from "@/images/icon.jpg";
import useGetLanguage from "@/pages/hook/useGetLanguage";
import "./index.scss";

export default function Index() {
  let language = useGetLanguage("login");
  
  const formSubmit = (e) => {
    const email = e.detail.value?.email?.toString();
    const password = e.detail.value?.password;

    if (!email || !password) {
      return Taro.showToast({
        title: "请填写完整！",
        icon: "none",
        duration: 2000,
      });
    }

    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return Taro.showToast({
        title: "请输入正确的邮箱格式",
        icon: "none",
        duration: 2000,
      });
    }

    // 添加密码长度验证
    if (password.length < 6 || password.length > 20) {
      return Taro.showToast({
        title: language["Password length error"] || "密码长度必须在6-20位之间",
        icon: "none",
        duration: 2000,
      });
    }

    Taro.request({
      url: `/api/user/login`,
      method: "POST",
      data: { email, password },
    })
      .then((res) => {
        if (res.data.success) {
          Taro.showToast({
            title: "登录成功",
            icon: "success",
            duration: 2000,
          });
          Taro.setStorageSync("token", res.data.data.token);
          Taro.redirectTo({
            url: "/pages/part1/select/index",
          });
        } else {
          let errorMessage = res.data.data || "登录失败";
          if (typeof errorMessage === 'object' && errorMessage.message) {
            errorMessage = errorMessage.message;
          }
          Taro.showToast({
            title: errorMessage,
            icon: "none",
            duration: 2000,
          });
        }
      })
      .catch((err) => {
        let errorMessage = err.data?.data || "登录失败";
        if (typeof errorMessage === 'object' && errorMessage.message) {
          errorMessage = errorMessage.message;
        }
        Taro.showToast({
          title: errorMessage,
          icon: "none",
          duration: 2000,
        });
      });
  };

  return (
    <Form onSubmit={formSubmit}>
      <View className="text-center py-2.5">
        <Image mode="widthFix" src={banner}></Image>
      </View>
      <View className="w-full h-full px-10 box-border">
        <View className="text-xl font-bold m-b-5 tracking-wider">
          {language["Welcome Back"]}
        </View>
        <View className="flex justify-between items-center w-full">
          <View className="i-ic-baseline-email text-gray-400 text-xl"></View>
          <Input
            type="text"
            name="email"
            className="w-full py-2 px-1 text-sm placeholder-gray-200"
            placeholder={language["Please enter your email"]}
          />
        </View>
        <View className="bg-gray-100 h-0.5 mb-1"></View>
        <View className="flex justify-between items-center w-full">
          <View className="i-ic-baseline-lock text-gray-400 text-xl"></View>
          <Input
            type="password"
            name="password"
            className="w-full py-2 px-1 text-sm placeholder-gray-200"
            placeholder={language["Please enter your password"]}
          />
        </View>
        <View className="bg-gray-100 h-0.5 mb-1"></View>
        <View className="flex justify-between items-center mb-4">
          <View
            className="text-[#1659c0] text-xs"
            onClick={() => {
              Taro.navigateTo({
                url: "/pages/part1/reset-password/index",
              });
            }}
          >
            {language["forget the password"]}
          </View>
          <View
            className="text-[#1659c0] text-xs"
            onClick={() => {
              Taro.redirectTo({
                url: "/pages/part1/register/index",
              });
            }}
          >
            {language["register"]}
          </View>
        </View>
        <Button
          formType="submit"
          className="bg-[#1659c0] text-white border-none btn"
        >
          {language["login"]}
        </Button>
      </View>
    </Form>
  );
}
