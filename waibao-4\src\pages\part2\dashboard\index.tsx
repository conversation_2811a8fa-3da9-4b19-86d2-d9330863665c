import { <PERSON><PERSON>, Image, Swiper, SwiperItem, View } from "@tarojs/components";
import useGetLanguage from "@/pages/hook/useGetLanguage";
import home from "@/images/home.png";
import homeGray from "@/images/home-gray.png";
import add from "@/images/add.png";
import addGray from "@/images/add-gray.png";
import { AtTabBar, AtTabs, AtTabsPane } from "taro-ui";
import { useState } from "react";
import Home from "./home";
import Create from "./create";
import "./index.scss";
import useLogin from "@/pages/hook/useLogin";
import Taro from "@tarojs/taro";

export default function Index() {
  let [pageIndex, setPageIndex] = useState(0);
  let [showMenu, setShowMenu] = useState(false);
  let language = useGetLanguage("Home");
  let token = useLogin();
  const pages = [<Home token={token}></Home>, <Create token={token}></Create>];

  const handleLogout = () => {
    Taro.clearStorageSync();
    Taro.redirectTo({ url: "/pages/part1/login/index" });
  };

  const handleSwitchRole = () => {
    Taro.redirectTo({ url: "/pages/part1/select/index" });
  };

  const handleUserCenter = () => {
    Taro.navigateTo({ url: "/pages/user/center/index" });
  };

  return (
    <View className="relative bg-gray-200">
      {/* 悬浮菜单按钮 */}
      <View 
        className="fixed right-4 top-4 z-50 w-10 h-10 bg-[#1659c0] rounded-full flex items-center justify-center shadow-lg"
        onClick={() => setShowMenu(!showMenu)}
      >
        <View className="i-ic-baseline-menu text-white text-2xl" />
      </View>

      {/* 悬浮菜单 */}
      {showMenu && (
        <View className="fixed right-4 top-16 z-50 bg-white rounded-lg shadow-lg overflow-hidden">
          <View 
            className="px-4 py-2 flex items-center hover:bg-gray-100 active:bg-gray-200"
            onClick={handleUserCenter}
          >
            <View className="i-ic-baseline-person mr-2 text-[#1659c0]" />
            <View className="text-sm text-gray-700">{language["User center"]}</View>
          </View>
          <View 
            className="px-4 py-2 flex items-center hover:bg-gray-100 active:bg-gray-200 border-t border-gray-100"
            onClick={handleSwitchRole}
          >
            <View className="i-ic-baseline-switch-account mr-2 text-[#1659c0]" />
            <View className="text-sm text-gray-700">{language["Switch role"]}</View>
          </View>
          <View 
            className="px-4 py-2 flex items-center hover:bg-gray-100 active:bg-gray-200 border-t border-gray-100"
            onClick={handleLogout}
          >
            <View className="i-ic-baseline-logout mr-2 text-[#1659c0]" />
            <View className="text-sm text-gray-700">{language["Logout"]}</View>
          </View>
        </View>
      )}

      {pages[pageIndex]}
      <AtTabBar
        fixed
        tabList={[
          {
            title: language["Home"],
            image: homeGray,
            selectedImage: home,
          },
          {
            title: language["Create task"],
            image: addGray,
            selectedImage: add,
          },
        ]}
        onClick={(num) => {
          setPageIndex(num);
        }}
        current={pageIndex}
      />
    </View>
  );
}
