import { PropsWithChildren } from "react";
import Taro, { useLaunch } from "@tarojs/taro";
import "uno.css";
import "./app.scss";

function App({ children }: PropsWithChildren<any>) {
  useLaunch(() => {
    const env = Taro.getEnv();
    console.log("当前环境：", env);
    const language = Taro.getStorageSync("language");

    if (!language) {
      Taro.setStorageSync("language", "English");
    }
    if (env === "WEB") {
      document.documentElement.style.fontSize = "16px"; //设置h5下html的字体大小
    }
  });
  return children;
}

export default App;
