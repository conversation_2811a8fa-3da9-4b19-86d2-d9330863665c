import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';

@Entity('invite_campaign')
export class InviteCampaign {
  @ApiProperty({ description: '活动ID' })
  @PrimaryGeneratedColumn()
  id: number;

  @ApiProperty({ description: '活动名称' })
  @Column({
    comment: '活动名称'
  })
  name: string;

  @ApiProperty({ description: '活动描述' })
  @Column({
    comment: '活动描述',
    type: 'text',
    nullable: true
  })
  description: string;

  @ApiProperty({ description: '开始时间', example: '2023-12-14T00:00:00Z' })
  @Column({
    comment: '开始时间',
    type: 'timestamp'
  })
  startTime: Date;

  @ApiProperty({ description: '结束时间', example: '2023-12-31T23:59:59Z' })
  @Column({
    comment: '结束时间',
    type: 'timestamp'
  })
  endTime: Date;

  @ApiProperty({ description: '基础奖励倍数', example: 1.5, default: 1 })
  @Column({
    comment: '基础奖励倍数',
    type: 'float',
    default: 1
  })
  rewardMultiplier: number;

  @ApiProperty({ description: '每日邀请上限', required: false })
  @Column({
    comment: '每日邀请上限',
    type: 'int',
    nullable: true
  })
  dailyInviteLimit: number;

  @ApiProperty({ description: '活动总邀请上限', required: false })
  @Column({
    comment: '活动总邀请上限',
    type: 'int',
    nullable: true
  })
  totalInviteLimit: number;

  @ApiProperty({ description: '当前总邀请数', default: 0 })
  @Column({
    comment: '当前总邀请数',
    default: 0
  })
  currentInvites: number;

  @ApiProperty({ description: '是否启用', default: true })
  @Column({
    comment: '是否启用',
    default: true
  })
  enabled: boolean;

  @ApiProperty({ description: '创建时间' })
  @CreateDateColumn()
  createdAt: Date;
} 