import { ApiProperty } from '@nestjs/swagger';
import { InviteCampaign } from '../entities/invite-campaign.entity';

export class CampaignStats {
  @ApiProperty({ description: '总邀请人数' })
  totalInvites: number;

  @ApiProperty({ description: '总奖励积分' })
  totalRewards: number;

  @ApiProperty({ description: '完成进度（百分比）', example: 65.5, nullable: true })
  progress: number | null;
}

export class CampaignStatsResponse {
  @ApiProperty({ description: '活动信息' })
  campaign: InviteCampaign;

  @ApiProperty({ description: '统计数据' })
  stats: CampaignStats;
} 