"use strict";function sendMessageToGroup(e){var o,n=JSON.stringify(e).length;return(0,mobile_1._invoke)("biz.util.callComponent",{componentType:"h5",params:{url:"/im/cool-app-component.html?corpId=".concat(encodeURIComponent(null===(o=null===e||void 0===e?void 0:e.context)||void 0===o?void 0:o.corpId),"#/send-message?params=").concat(encodeURIComponent(JSON.stringify({body:e,bodyLengthList:[n]}))),target:"float",title:"提示",wnId:"sendMessageToGroup",panelHeight:"percent83"}})}Object.defineProperty(exports,"__esModule",{value:!0}),exports.sendMessageToGroup=void 0,require("../../entry/union");var mobile_1=require("../../entry/mobile");exports.sendMessageToGroup=sendMessageToGroup;