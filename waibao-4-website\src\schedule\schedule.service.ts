import { Injectable } from '@nestjs/common';
import { CreateScheduleDto } from './dto/create-schedule.dto';
import { UpdateScheduleDto } from './dto/update-schedule.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { Schedule } from './entities/schedule.entity';
import { In, Repository } from 'typeorm';
import { Request } from 'express';
import { Broadcastroom } from 'src/broadcastroom/entities/broadcastroom.entity';

@Injectable()
export class ScheduleService {
  constructor(
    @InjectRepository(Schedule)
    private readonly scheduleRepository: Repository<Schedule>,
    @InjectRepository(Broadcastroom)
    private readonly broadcastroomRepository: Repository<Broadcastroom>,
  ) {}
  create(createScheduleDto: CreateScheduleDto, user_id: number) {
    return this.scheduleRepository.save({
      ...createScheduleDto,
      authorId: user_id,
    });
  }

  async findAll(user_id) {
    let boradcasts = await this.scheduleRepository.find({
      select: ['broadcastroomId'],
      where: { authorId: user_id },
    });
    let boradcastroomIds = boradcasts.map((item) => item.broadcastroomId);
    return await this.broadcastroomRepository.find({
      where: { id: In(boradcastroomIds)},
    });
  }

  findOne(id: number, user_id) {
    return this.scheduleRepository.findBy({
      broadcastroomId: id,
      authorId: user_id,
    });
  }

  update(id: number, updateScheduleDto: UpdateScheduleDto) {
    return `This action updates a #${id} schedule`;
  }

  remove(id: number) {
    return `This action removes a #${id} schedule`;
  }
}
