import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNumber, IsDateString, IsOptional } from 'class-validator';

export class CreateCampaignRequest {
  @ApiProperty({ 
    description: '活动名称',
    example: '新年邀请活动'
  })
  @IsString()
  name: string;

  @ApiProperty({ 
    description: '活动描述',
    example: '邀请好友注册，赢取双倍积分！',
    required: false
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({ 
    description: '开始时间',
    example: '2024-01-01T00:00:00Z'
  })
  @IsDateString()
  startTime: string;

  @ApiProperty({ 
    description: '结束时间',
    example: '2024-01-31T23:59:59Z'
  })
  @IsDateString()
  endTime: string;

  @ApiProperty({ 
    description: '基础奖励倍数',
    example: 2.0,
    default: 1
  })
  @IsNumber()
  @IsOptional()
  rewardMultiplier?: number;

  @ApiProperty({ 
    description: '每日邀请上限',
    example: 20,
    required: false
  })
  @IsNumber()
  @IsOptional()
  dailyInviteLimit?: number;

  @ApiProperty({ 
    description: '活动总邀请上限',
    example: 1000,
    required: false
  })
  @IsNumber()
  @IsOptional()
  totalInviteLimit?: number;
} 