import { Controller, Get, Put, Body } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiProperty } from '@nestjs/swagger';
import { InviteService } from './invite.service';
import { RewardConfig } from './entities/reward-config.entity';
import { IsNumber, IsString, IsOptional, Min, Max } from 'class-validator';

// 创建一个专门的响应 DTO 来优化文档显示
class RewardConfigResponse {
  @ApiProperty({ description: '配置ID', example: 1 })
  id: number;

  @ApiProperty({ description: '配置名称', example: '基础奖励配置' })
  name: string;

  @ApiProperty({ 
    description: '邀请人获得的奖励积分（邀请成功后）', 
    example: 1,
    minimum: 1
  })
  inviterReward: number;

  @ApiProperty({ 
    description: '被邀请人获得的奖励积分（注册成功后）', 
    example: 1,
    minimum: 1
  })
  inviteeReward: number;

  @ApiProperty({ 
    description: '创建任务获得的奖励积分', 
    example: 1,
    minimum: 1
  })
  taskCreatorReward: number;

  @ApiProperty({ 
    description: '参与任务获得的奖励积分', 
    example: 1,
    minimum: 1
  })
  taskParticipantReward: number;

  @ApiProperty({ 
    description: '邀请人获得的任务奖励分成比例（百分比）', 
    example: 1,
    minimum: 1,
    maximum: 100
  })
  taskCommissionRate: number;

  @ApiProperty({ 
    description: '配置说明', 
    example: '系统默认奖励配置',
    required: false
  })
  description: string;

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;
}

// 创建一个专门的更新 DTO
class UpdateRewardConfigDto {
  @IsNumber()
  @Min(1)
  @IsOptional()
  @ApiProperty({ 
    description: '邀请人获得的奖励积分', 
    example: 1,
    minimum: 1,
    required: false
  })
  inviterReward?: number;

  @IsNumber()
  @Min(1)
  @IsOptional()
  @ApiProperty({ 
    description: '被邀请人获得的奖励积分', 
    example: 1,
    minimum: 1,
    required: false
  })
  inviteeReward?: number;

  @IsNumber()
  @Min(1)
  @IsOptional()
  @ApiProperty({ 
    description: '创建任务获得的奖励积分', 
    example: 1,
    minimum: 1,
    required: false
  })
  taskCreatorReward?: number;

  @IsNumber()
  @Min(1)
  @IsOptional()
  @ApiProperty({ 
    description: '参与任务获得的奖励积分', 
    example: 1,
    minimum: 1,
    required: false
  })
  taskParticipantReward?: number;

  @IsNumber()
  @Min(1)
  @Max(100)
  @IsOptional()
  @ApiProperty({ 
    description: '邀请人获得的任务奖励分成比例（百分比）', 
    example: 1,
    minimum: 1,
    maximum: 100,
    required: false
  })
  taskCommissionRate?: number;

  @IsString()
  @IsOptional()
  @ApiProperty({ 
    description: '配置说明', 
    example: '系统默认奖励配置',
    required: false
  })
  description?: string;
}

@Controller('reward-config')
@ApiTags('奖励配置')
export class RewardConfigController {
  constructor(private readonly inviteService: InviteService) {}

  @Get()
  @ApiOperation({ 
    summary: '获取奖励配置',
    description: '获取系统当前的奖励积分配置，包括邀请奖励、任务奖励等'
  })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    type: RewardConfigResponse
  })
  findOne() {
    return this.inviteService.getRewardConfig();
  }

  @Put('update')
  @ApiOperation({ 
    summary: '更新奖励配置',
    description: `
    更新系统的奖励积分配置。
    - 所有字段都是可选的，只更新提供的字段
    - 所有奖励积分必须大于等于1
    - 分成比例必须在1-100之间
    - 更新后立即生效
    `
  })
  @ApiResponse({
    status: 200,
    description: '更新成功',
    type: RewardConfigResponse
  })
  update(@Body() config: UpdateRewardConfigDto) {
    return this.inviteService.updateRewardConfig(config);
  }
} 