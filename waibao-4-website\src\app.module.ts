import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserModule } from './user/user.module';
import { JwtModule } from '@nestjs/jwt';
import { BroadcastroomModule } from './broadcastroom/broadcastroom.module';
import { ConfigModule } from '@nestjs/config';
import { getConfig } from './utils';
import { APP_GUARD } from '@nestjs/core';
import { LoginGuard } from './login.guard';
import { CaptchaModule } from './captcha/captcha.module';
import { LoggerModule } from './logger/logger.module';
import { Schedule2Module } from './schedule/schedule.module';
import { ScheduleModule } from '@nestjs/schedule';
import { AdminModule } from './admin/admin.module';
import { EmailModule } from './email/email.module';
const config = getConfig();
console.log(config);
@Module({
  imports: [
    ScheduleModule.forRoot(),
    ConfigModule.forRoot({
      ignoreEnvFile: true,
      isGlobal: true,
      load: [getConfig],
    }),
    TypeOrmModule.forRoot({
      type: config.db.mysql.type,
      host: config.db.mysql.host,
      port: config.db.mysql.port,
      username: config.db.mysql.username,
      password: config.db.mysql.password,
      database: config.db.mysql.database,
      connectorPackage: config.db.mysql.connectorPackage,
      timezone: config.db.mysql.timezone,
      poolSize: config.db.mysql.poolSize,
      extra: {
        charset: 'utf8mb4'
      },
      autoLoadEntities: true,
      synchronize: true,
    }),
    JwtModule.register({
      global: true,
      secret: 'auiweuif0923&*^&%&',
      signOptions: {
        expiresIn: '7d',
      },
    }),

    UserModule,
    BroadcastroomModule,
    CaptchaModule,
    LoggerModule,
    Schedule2Module,
    AdminModule,
    EmailModule,
  ],

  controllers: [AppController],
  providers: [
    AppService,
    {
      provide: APP_GUARD,
      useClass: LoginGuard,
    }
  ],
})
export class AppModule {}
