"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var noop=function(){},webviewInMiniappBridge=function(e,n){return new Promise(function(r,i){var o=n.onSuccess||noop,a=n.onFail||noop;if(delete n.onSuccess,delete n.onFail,AlipayJSBridge){var p=e.split("."),l=p.pop()||"",t=p.join(".");AlipayJSBridge.call.apply(null,["webDdExec",{serviceName:t,actionName:l,args:n},function(e){var n={},p=e.content;if(p)try{n=JSON.parse(p)}catch(e){console.error("parse dt api result error",p,e)}e.success?(o.apply(null,[n]),r(n)):(a.apply(null,[n]),i(n))}])}else{var c=new Error("Fatal error, cannot find bridge ,current env is WebView in MiniApp");a(c),i(c)}})};exports.default=webviewInMiniappBridge;