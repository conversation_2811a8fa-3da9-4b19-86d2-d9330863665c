import { Modu<PERSON> } from '@nestjs/common';
import { InviteService } from './invite.service';
import { InviteController } from './invite.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { InviteRecord } from './entities/invite.entity';
import { User } from '../user/entities/user.entity';
import { InviteConfig } from './entities/invite-config.entity';
import { RewardConfig } from './entities/reward-config.entity';
import { RewardConfigController } from './reward-config.controller';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      InviteRecord,
      User,
      InviteConfig,
      RewardConfig
    ])
  ],
  controllers: [InviteController, RewardConfigController],
  providers: [InviteService],
  exports: [InviteService]
})
export class InviteModule {} 