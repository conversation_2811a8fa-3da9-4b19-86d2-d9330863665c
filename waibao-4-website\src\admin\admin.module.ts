import { Modu<PERSON> } from '@nestjs/common';
import { AdminController } from './admin.controller';
import { AdminService } from './admin.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from '../user/entities/user.entity';
import { Broadcastroom } from '../broadcastroom/entities/broadcastroom.entity';
import { UserModule } from '../user/user.module';
import { EmailModule } from '../email/email.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([User, Broadcastroom]),
    UserModule,
    EmailModule
  ],
  controllers: [AdminController],
  providers: [AdminService],
})
export class AdminModule {} 