import { View } from "@tarojs/components";
import Card from "@/pages/part2/dashboard/card";
import useGetLanguage from "@/pages/hook/useGetLanguage";
import useLogin from "@/pages/hook/useLogin";
import { useEffect, useState } from "react";
import Taro from "@tarojs/taro";
import Map from "./map";
import "./index.scss";
import { BackButton } from '@/components/BackButton'

const Sign = ({ item }) => {
  return (
    <View>
      <View className="bg-gray-200 mt-3 rounded-md p3 ">
        <View className=" text-gray-400 font-bold mb-2">{item.date}</View>
        <View className="flex justify-start item-center">
          <View className="i-emojione-white-heavy-check-mark text-2xl mt-2"></View>
          <View className="text-center w-[20%]">
            <View className="font-bold">
              {new Date(item.time * 1).toLocaleString().split(" ")[1]}
            </View>
            <View className="text-gray-400 text-xs">{item.action}</View>
          </View>
          <View className="w-[50%] ml-5">
            <View className="text-gray-800 ">{item.address}</View>
            <View className="text-gray-400 text-xs">已完成</View>
          </View>
        </View>
      </View>
    </View>
  );
};

export default function Index() {
  const [canSign, setCanSign] = useState(false);
  const [locationData, setLocationData] = useState(null);
  let language = useGetLanguage("Task details");
  let token = useLogin();
  let [datas, setDatas] = useState<
    {
      id: number;
      day: string;
      time: string;
      content: string;
      address: string;
    }[]
  >([]);

  let params: Record<string, string> = {};
  try {
    const hash = window.location.hash.replace("#/", "");
    const queryIndex = hash.indexOf("?");
    if (queryIndex > -1) {
      const queryString = hash.substring(queryIndex + 1);
      new URLSearchParams(queryString).forEach((value, key) => {
        params[key] = value;
      });
    }
  } catch (error) {
    console.error("URL parsing error:", error);
    params = {
      id: "",
      status: "",
      startTime: "",
      endTime: "",
      address: "",
      title: "",
      content: "",
      time: "",
      phone: "",
    };
  }

  const cardDatas = {
    id: params.id || "",
    status: params.status || "",
    startTime: params.startTime || "",
    endTime: params.endTime || "",
    address: params.address || "",
    title: params.title || "",
    content: params.content || "",
    time: params.time || "",
    phone: params.phone || "",
  };

  function getData() {
    if (!cardDatas.id) {
      console.warn("No task ID available");
      return;
    }

    Taro.request({
      url: process.env.TARO_APP_HOST + "/api/schedule/" + cardDatas.id,
      method: "GET",
      header: {
        token,
      },
    }).then((res) => {
      if (res.data.data) {
        setDatas(res.data.data.reverse());
      }
    }).catch(err => {
      console.error("Failed to fetch schedule data:", err);
    });
  }

  useEffect(() => {
    getData();
  }, []);

  const handleFulfil = () => {
    if (!canSign || !locationData) {
      Taro.showToast({
        title: '请先完成位置定位',
        icon: 'none'
      });
      return;
    }

    // 执行签到逻辑
    const token = Taro.getStorageSync('token');
    const params = Taro.getCurrentInstance().router?.params;
    
    Taro.request({
      url: `${process.env.TARO_APP_HOST}/api/schedule/sign`,
      method: 'POST',
      header: { token },
      data: {
        taskId: params?.id,
        ...locationData
      }
    }).then(res => {
      if (res.data.success) {
        Taro.showToast({
          title: '签到成功',
          icon: 'success'
        });
        // 可以在这里刷新签到记录
      } else {
        Taro.showToast({
          title: res.data.message || '签到失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      console.error('签到失败:', err);
      Taro.showToast({
        title: '签到失败',
        icon: 'none'
      });
    });
  };

  return (
    <View className="page-gradient box-border pt-3 relative pb-30">
      <BackButton />
      <View className="bg-white rounded-xl mx-auto w-[90%] overflow-hidden mb-4 p-4">
        <View className="text-xl font-bold text-gray-800 mb-3">
          {cardDatas.title}
        </View>
        
        <View className="flex items-center text-gray-600 mb-2">
          <View className="i-ic-baseline-location-on mr-1" />
          {cardDatas.address}
        </View>
        
        <View className="text-gray-600 mb-4">
          {cardDatas.startTime} - {cardDatas.endTime}
        </View>

        <View className="bg-blue-50 p-4 rounded-lg">
          <View className="text-base font-medium text-gray-700 mb-2 flex items-center">
            <View className="i-ic-outline-description mr-2 text-[#1659c0]" />
            {language["Activity content"]}
          </View>
          <View className="text-gray-600 whitespace-pre-wrap">
            {cardDatas.content}
          </View>
        </View>

        <View className="mt-4 flex items-center text-gray-600">
          <View className="i-ic-baseline-phone mr-1" />
          {cardDatas.phone}
        </View>
      </View>

      <View className="p-4 pt-0 relative box-border mx-auto overflow-hidden rounded-xl">
        <Map 
          onLocationCheck={setCanSign}
          onSignIn={setLocationData}
        />
      </View>
      <View className="bg-white rounded-xl mx-auto w-[90%] overflow-hidden text-left text-sm p-4 relative box-border ">
        <View className=" text-lg font-bold">
          <View className="i-ic-outline-library-books inline-block text-gray-700 text-lg align-middle"></View>
          {language["Punching card record"]}
        </View>
        <View>
          {datas.map((item) => (
            <Sign key={item.id} item={item}></Sign>
          ))}
        </View>
      </View>
   
      <View 
        className={`text-center py-3 rounded-lg ${
          canSign ? 'bg-[#1659c0] text-white' : 'bg-gray-300 text-gray-500'
        }`}
        onClick={handleFulfil}
      >
        {language["Fulfil"]}
      </View>
    </View>
  );
}
