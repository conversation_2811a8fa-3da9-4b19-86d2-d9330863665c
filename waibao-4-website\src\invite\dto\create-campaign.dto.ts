import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNumber, IsDateString, IsOptional } from 'class-validator';

export class CreateCampaignDto {
  @ApiProperty({ description: '活动名称' })
  @IsString()
  name: string;

  @ApiProperty({ description: '活动描述', required: false })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({ description: '开始时间', example: '2023-12-14T00:00:00Z' })
  @IsDateString()
  startTime: string;

  @ApiProperty({ description: '结束时间', example: '2023-12-31T23:59:59Z' })
  @IsDateString()
  endTime: string;

  @ApiProperty({ description: '奖励倍数', example: 1.5, default: 1 })
  @IsNumber()
  @IsOptional()
  rewardMultiplier?: number;
} 