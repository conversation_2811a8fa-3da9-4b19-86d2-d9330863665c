"use strict";function installCoolAppToGroup(o){return(0,mobile_1._invoke)("biz.util.callComponent",{componentType:"h5",params:{url:"/resource-picker/".concat(utils_1.isMobile?"mob":"index",".html?scene=addCoolAppToGroup&params=").concat(encodeURIComponent(JSON.stringify(o))),target:utils_1.isMobile?"":"float",title:"选择群添加应用",wnId:"addCoolAppToGroup",panelHeight:"percent90"}})}Object.defineProperty(exports,"__esModule",{value:!0}),exports.installCoolAppToGroup=void 0,require("../../entry/union");var mobile_1=require("../../entry/mobile"),utils_1=require("./utils");exports.installCoolAppToGroup=installCoolAppToGroup;