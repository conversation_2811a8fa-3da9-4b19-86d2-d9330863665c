import { Controller, Post, Body, Get, Param, Patch, Delete, Query } from '@nestjs/common';
import { AdminService } from './admin.service';
import { ApiOperation, ApiTags, ApiResponse } from '@nestjs/swagger';
import { LoginDto } from './dto/login.dto';
import { RequireAdmin } from 'src/custom-decorator';
import { DeleteUserDto } from './dto/user-management.dto';
import { UserQueryDto } from './dto/user-management.dto';
import { TaskQueryDto } from './dto/task-management.dto';

@Controller('admin')
@ApiTags('管理员接口')
export class AdminController {
  constructor(private readonly adminService: AdminService) {}

  @Post('login')
  @ApiOperation({ summary: '管理员登录' })
  async login(@Body() loginDto: LoginDto) {
    return this.adminService.login(loginDto);
  }

  @Get('users')
  @RequireAdmin()
  @ApiOperation({ summary: '获取待审核用户列表' })
  async getPendingUsers() {
    return this.adminService.getPendingUsers();
  }

  @Patch('user/:id')
  @RequireAdmin()
  @ApiOperation({ summary: '审核用户' })
  async approveUser(@Param('id') id: string) {
    return this.adminService.approveUser(+id);
  }

  @Get('tasks') 
  @RequireAdmin()
  @ApiOperation({ summary: '获取待审核任务列表' })
  async getPendingTasks() {
    return this.adminService.getPendingTasks();
  }

  @Patch('task/:id')
  @RequireAdmin()
  @ApiOperation({ summary: '审核任务' })
  async approveTask(
    @Param('id') id: string,
    @Body('approved') approved: boolean
  ) {
    return this.adminService.approveTask(+id, approved);
  }

  // 新增用户管理接口
  @Get('all-users')
  @RequireAdmin()
  @ApiOperation({ summary: '获取所有用户列表' })
  @ApiResponse({ status: 200, description: '返回分页的用户列表' })
  async getAllUsers(@Query() query: UserQueryDto) {
    return this.adminService.getAllUsers(query);
  }

  @Get('user/:id')
  @RequireAdmin()
  @ApiOperation({ summary: '获取单个用户详情' })
  @ApiResponse({ status: 200, description: '返回用户详情' })
  @ApiResponse({ status: 404, description: '用户不存在' })
  async getUserById(@Param('id') id: string) {
    return this.adminService.getUserById(+id);
  }

  @Delete('user/:id')
  @RequireAdmin()
  @ApiOperation({ summary: '删除用户' })
  @ApiResponse({ status: 200, description: '用户删除成功' })
  @ApiResponse({ status: 404, description: '用户不存在' })
  async deleteUser(@Param('id') id: string) {
    return this.adminService.deleteUser(+id);
  }

  // 新增任务管理接口
  @Get('all-tasks')
  @RequireAdmin()
  @ApiOperation({ summary: '获取所有任务列表' })
  @ApiResponse({ status: 200, description: '返回分页的任务列表' })
  async getAllTasks(@Query() query: TaskQueryDto) {
    return this.adminService.getAllTasks(query);
  }

  @Get('task-detail/:id')
  @RequireAdmin()
  @ApiOperation({ summary: '获取单个任务详情' })
  @ApiResponse({ status: 200, description: '返回任务详情' })
  @ApiResponse({ status: 404, description: '任务不存在' })
  async getTaskById(@Param('id') id: string) {
    return this.adminService.getTaskById(+id);
  }
} 