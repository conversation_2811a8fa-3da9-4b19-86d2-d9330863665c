@import "~taro-ui/dist/style/components/search-bar.scss";
@import "~taro-ui/dist/style/components/button.scss";
@import "~taro-ui/dist/style/components/icon.scss";

.at-search-bar {
  background-color: transparent;
}

.at-search-bar::after {
  display: none;
}
.page-gradient {
  background: linear-gradient(
    to top,
    rgb(241, 241, 241) 60%,
    #165ac018 70%,
    #165ac054 100%
  );
}

.bg-gradient-to-r2 {
  background: linear-gradient(
    to right,
    rgba(255, 255, 255, 0.205) 30%,
    #165ac01f 80%,
    #165ac041 100%
  );
}
.bg-gradient-to-r3 {
  background: linear-gradient(to right, #165ac01f 0%, #165ac041 100%);
}

.scroll-view {
  width: 100%; /* 100% 宽度，使其充满整个容器 */
  white-space: nowrap; /* 让子项在同一行显示 */
}

.scroll-item {
  display: inline-block; /* 内联块级元素，使其横向排列 */
  width: 60%; /* 设置每个滚动项的宽度 */
  margin-right: 10px; /* 为滚动项之间留出一些间隙 */
  box-sizing: border-box;
  height: 90%;
  margin-top: 20rpx;
}

.bg-gradient-to-r4 {
  background: linear-gradient(0deg, #165ac01f 0%, #165ac041 100%);
}

.bg-gradient-to-r5 {
  background: linear-gradient(0deg, #c016961f 0%, #c016b241 100%);
}

.bg-gradient-to-r6 {
  background: linear-gradient(0deg, #c0bd161f 0%, #a7c01641 100%);
}

.icon-deg45{
  transform: rotate(45deg);
}