import { View } from "@tarojs/components";
import { AtButton } from "taro-ui";
import Taro, { useLaunch, useLoad } from "@tarojs/taro";
import { useEffect, useState } from "react";
import "./index.scss";

export default function Index() {
  let [lan,setLan] = useState("English");
  function goto(pathName) {
    Taro.navigateTo({
      url: `/pages/${pathName}/index`,
    });
  }
  useEffect(() => {
      var language = Taro.getStorageSync("language");
      setLan(language);
  }, []);
  return (
    <View>
      当前语言：{lan}
      <AtButton type="primary" onClick={goto.bind(null, "part3/recommend")}>
        首页
      </AtButton>
      <AtButton type="primary" onClick={goto.bind(null, "part3/list")}>
        任务列表
      </AtButton>
      <AtButton type="primary" onClick={goto.bind(null, "part3/detail")}>
        任务详情
      </AtButton>
      <AtButton type="primary" onClick={goto.bind(null, "part1/register")}>
        注册
      </AtButton>
      <AtButton type="primary" onClick={goto.bind(null, "part1/login")}>
        登录
      </AtButton>
      <AtButton type="primary" onClick={goto.bind(null, "part1/select")}>
        角色
      </AtButton>
      <AtButton type="primary" onClick={goto.bind(null, "part2/dashboard")}>
        发布
      </AtButton>
      <AtButton type="primary" onClick={goto.bind(null, "agreement/privacy")}>
        隐私协议
      </AtButton>
      <AtButton type="primary" onClick={goto.bind(null, "agreement/user")}>
        用户协议
      </AtButton>
      <AtButton
        type="primary"
        onClick={() => {
          Taro.setStorageSync("language", "zh_CN");
          Taro.reLaunch({
            url: "/pages/index/index",
          });
        }}
      >
        切换中文
      </AtButton>
      <AtButton
        type="primary"
        onClick={() => {
          Taro.setStorageSync("language", "English");
          Taro.reLaunch({
            url: "/pages/index/index",
          });
        }}
      >
        切换英语
      </AtButton>
      <AtButton
        type="primary"
        onClick={() => {
          Taro.reLaunch({
            url: "/pages/index/index",
          });
        }}
      >
        刷新测试
      </AtButton>
    </View>
  );
}
