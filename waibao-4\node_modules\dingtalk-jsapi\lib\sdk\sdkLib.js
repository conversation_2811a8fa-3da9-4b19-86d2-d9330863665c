"use strict";function isFunction(o){return"function"==typeof o}function compareVersion(o,n){function e(o){return parseInt(o,10)||0}for(var r=o.split(".").map(e),E=n.split(".").map(e),t=0;t<r.length;t++){if(void 0===E[t])return!1;if(r[t]<E[t])return!1;if(r[t]>E[t])return!0}return!0}Object.defineProperty(exports,"__esModule",{value:!0}),exports.LogLevel=exports.APP_TYPE=exports.ENV_ENUM_SUB=exports.ENV_ENUM=exports.ERROR_CODE=exports.compareVersion=exports.isFunction=void 0,exports.isFunction=isFunction,exports.compareVersion=compareVersion;var ERROR_CODE;!function(o){o.cancel="-1",o.not_exist="1",o.no_permission="7",o.jsapi_internal_error="22"}(ERROR_CODE||(exports.ERROR_CODE=ERROR_CODE={}));var ENV_ENUM;!function(o){o.pc="pc",o.android="android",o.ios="ios",o.notInDingTalk="notInDingTalk",o.harmony="harmony"}(ENV_ENUM||(exports.ENV_ENUM=ENV_ENUM={}));var ENV_ENUM_SUB;!function(o){o.mac="mac",o.win="win",o.noSub="noSub"}(ENV_ENUM_SUB||(exports.ENV_ENUM_SUB=ENV_ENUM_SUB={}));var APP_TYPE;!function(o){o.WEB="WEB",o.MINI_APP="MINI_APP",o.WEEX="WEEX",o.WEBVIEW_IN_MINIAPP="WEBVIEW_IN_MINIAPP",o.WEEX_WIDGET="WEEX_WIDGET"}(APP_TYPE||(exports.APP_TYPE=APP_TYPE={}));var LogLevel;!function(o){o[o.INFO=1]="INFO",o[o.WARNING=2]="WARNING",o[o.ERROR=3]="ERROR"}(LogLevel||(exports.LogLevel=LogLevel={}));