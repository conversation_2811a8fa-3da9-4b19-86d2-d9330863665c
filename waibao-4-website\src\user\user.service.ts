import {
  HttpException,
  Injectable,
  Logger,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { InjectEntityManager, InjectRepository } from '@nestjs/typeorm';
import { User } from './entities/user.entity';
import { EntityManager, In, Repository } from 'typeorm';
import * as crypto from 'crypto';
import { RegisterDto } from './dto/register.dto';
import { LoginDto } from './dto/login.dto';
import { InviteService } from '../invite/invite.service';
import { EmailService } from '../email/email.service';
import { ResetPasswordDto } from './dto/reset-password.dto';
import { JwtService } from '@nestjs/jwt';

function md5(str) {
  const hash = crypto.createHash('md5');
  hash.update(str);
  return hash.digest('hex');
}

@Injectable()
export class UserService {
  private logger = new Logger(UserService.name);

  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectEntityManager()
    private readonly entityManager: EntityManager,
    private readonly inviteService: InviteService,
    private readonly emailService: EmailService,
    private readonly jwtService: JwtService
  ) {}

  async login(loginDto: LoginDto) {
    try {
      this.logger.log(`User login attempt: ${loginDto.email}`);
      
      const user = await this.userRepository.findOne({
        where: [
          { email: loginDto.email },
          { username: loginDto.email }
        ],
        select: ['id', 'username', 'password', 'approved', 'omn', 'email', 'phone', 'isAdmin', 'wxOpenid']
      });

      if (!user) {
        throw new UnauthorizedException('用户不存在');
      }

      // 检查用户是否已通过审核
      if (!user.approved) {
        throw new UnauthorizedException('您的账号正在审核中，请等待管理员审核');
      }

      if (user.password !== md5(loginDto.password)) {
        throw new UnauthorizedException('密码错误');
      }

      const token = await this.jwtService.signAsync({
        user: {
          id: user.id,
          username: user.username,
          omn: user.omn
        }
      });

      this.logger.log(`User ${user.id} logged in successfully`);
      return {
        token,
        id: user.id,
        email: user.email,
        username: user.username,
        phone: user.phone,
        omn: user.omn,
        isAdmin: user.isAdmin,
        wxOpenid: user.wxOpenid
      };
    } catch (error) {
      this.logger.error(`Login error for ${loginDto.email}: ${error.message}`);
      throw error;
    }
  }

  async loginFlag(id: number, loginout: Boolean) {
    const userInfo = await this.userRepository.preload({
      id,
      loginout,
    });
    if (!userInfo) {
      throw new NotFoundException(`userInfo ${id} 没有找到!`);
    }
    return this.userRepository.save(userInfo);
  }

  async loginout(user) {
    return await this.loginFlag(user.id, true); //告诉数据库，该账户目前登出
  }

  private generateInviteCode(): string {
    return Math.random().toString(36).substring(2, 10).toUpperCase();
  }

  async register(user: RegisterDto, ip: string, deviceId: string) {
    try {
      this.logger.log(`开始处理用户 ${user.email} 的注册流程`);
      
      // 1. 验证邮箱格式
      if (!this.validateEmail(user.email)) {
        this.logger.warn(`邮箱格式验证失败: ${user.email}`);
        throw new HttpException('邮箱格式不正确', 401);
      }

      // 2. 检查用户是否已存在
      const [emailExists, phoneExists] = await Promise.all([
        this.userRepository.findOneBy({ email: user.email }),
        this.userRepository.findOneBy({ phone: user.phone })
      ]);

      if (emailExists) {
        this.logger.warn(`邮箱已存在: ${user.email}`);
        throw new HttpException('该邮箱已注册！', 401);
      }

      if (phoneExists) {
        this.logger.warn(`手机号已存在: ${user.phone}`);
        throw new HttpException('该手机号已注册！', 401);
      }

      // 3. 验证邮箱验证码
      this.logger.log(`验证邮箱验证码: ${user.email}`);
      const isValidCode = await this.emailService.verifyCode(user.email, user.verificationCode);
      if (!isValidCode) {
        this.logger.warn(`验证码验证失败: ${user.email}`);
        throw new HttpException('验证码无效或已过期', 401);
      }
      this.logger.log(`验证码验证成功: ${user.email}`);

      // 4. 处理邀请关系
      let inviter = null;
      if (user.inviterCode) {
        this.logger.log(`处理邀请关系，邀请码: ${user.inviterCode}`);
        inviter = await this.userRepository.findOne({
          where: { inviteCode: user.inviterCode }
        });
        
        if (!inviter) {
          this.logger.warn(`无效的邀请码: ${user.inviterCode}`);
          throw new HttpException('无效的邀请码', 400);
        }

        const isValid = await this.validateInviteCode(user.inviterCode);
        if (!isValid) {
          this.logger.warn(`邀请次数已达上限: ${user.inviterCode}`);
          throw new HttpException('邀请次数已达上限', 400);
        }
        this.logger.log(`邀请关系验证成功，邀请人ID: ${inviter.id}`);
      }

      // 5. 创建新用户
      const newUser = new User();
      newUser.email = user.email;
      newUser.phone = user.phone;
      newUser.username = user.email;
      newUser.password = md5(user.password);
      newUser.inviteCode = this.generateInviteCode();
      newUser.ip = ip;
      newUser.deviceId = deviceId;
      newUser.approved = false;

      if (inviter) {
        newUser.inviterId = inviter.id;
      }

      // 6. 保存用户并处理邀请奖励
      this.logger.log(`保存新用户: ${user.email}`);
      const savedUser = await this.userRepository.save(newUser);
      this.logger.log(`用户创建成功，ID: ${savedUser.id}`);

      // 7. 如果有邀请人，创建邀请记录
      if (inviter) {
        this.logger.log(`记录邀请奖励，邀请人ID: ${inviter.id}，被邀请人ID: ${savedUser.id}`);
        await this.inviteService.recordInviteReward({
          inviterId: inviter.id,
          inviteeId: savedUser.id,
          points: 0,
          rewardType: 'REGISTER'
        });
      }

      return '注册成功';
    } catch (error) {
      this.logger.error(`注册过程发生错误: ${error.message}`, error.stack);
      
      // 转发原始错误，保持HTTP状态码
      if (error instanceof HttpException) {
        throw error;
      }
      
      // 未知错误则返回通用错误信息
      throw new HttpException('注册失败，请稍后重试', 500);
    }
  }

  // 验证邮箱格式
  private validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  create(createUserDto: CreateUserDto) {
    const user = this.userRepository.create(createUserDto);
    return this.userRepository.save(user);
  }

  findAll() {
    return this.userRepository.find({
      relations: ['role'],
    });
  }

  async findOne(id: number) {
    // 处理系统管理员特殊情况
    if (id === 0) {
      return {
        id: 0,
        username: 'admin',
        isAdmin: true,
        omn: 0
      } as User;
    }

    const user = await this.userRepository
      .createQueryBuilder('user')
      .select([
        'user.id',
        'user.username',
        'user.password',
        'user.phone',
        'user.email',
        'user.name',
        'user.omn',
        'user.ip',
        'user.loginout',
        'user.wxOpenid',
        'user.approved',
        'user.inviteCode',
        'user.inviterId',
        'user.qrCode',
        'user.dailyInviteCount',
        'user.totalInviteCount',
        'user.lastInviteCountResetDate',
        'user.isAdmin'
      ])
      .where('user.id = :id', { id })
      .getOne();

    if (!user) {
      throw new NotFoundException(`用户 ${id} 不存在`);
    }

    return user;
  }

  async update(id: number, updateUserDto: UpdateUserDto) {
    try {
      this.logger.log(`Starting update for user ${id}, data: ${JSON.stringify(updateUserDto)}`);
      
      const user = await this.userRepository.findOne({
        where: { id },
        select: [
          'id',
          'username',
          'approved',
          'inviterId',
          'dailyInviteCount',
          'totalInviteCount',
          'lastInviteCountResetDate',
          'omn'
        ]
      });

      if (!user) {
        throw new NotFoundException(`User ${id} not found`);
      }

      const wasApproved = user.approved;
      
      // 合并更新数据
      Object.assign(user, updateUserDto);

      // 如果用户被审核通过
      if (!wasApproved && updateUserDto.approved === true) {
        this.logger.log(`User ${id} is being approved, inviterId: ${user.inviterId}`);
        
        // 获取奖励配置
        const config = await this.inviteService.getRewardConfig();
        
        // 设置被邀请人的初始积分
        user.omn = (user.omn || 0) + config.inviteeReward;
        this.logger.log(`Setting initial points for approved user: ${config.inviteeReward}`);

        // 先保存用户状态
        const savedUser = await this.userRepository.save(user);
        this.logger.log(`User ${id} status updated to approved, saved user:`, savedUser);

        // 记录被邀请人获得的奖励
        if (user.inviterId) {
          await this.inviteService.recordInviteReward({
            inviterId: user.inviterId,
            inviteeId: id,
            points: config.inviteeReward,
            rewardType: 'REGISTER_BONUS'
          });
        }

        // 如果有邀请人，处理邀请奖励
        if (user.inviterId) {
          this.logger.log(`Calling invite service to process rewards for user ${id} with inviter ${user.inviterId}`);
          try {
            await this.inviteService.handleInviteReward(user.inviterId, id).catch(error => {
              this.logger.error('Error in handleInviteReward:', error);
              throw error;
            });
            this.logger.log(`Successfully processed invite rewards for user ${id}`);
          } catch (error) {
            this.logger.error(`Error processing invite rewards: ${error.message}`, error.stack);
            // 不抛出错误，让审核流程继续
          }
        } else {
          this.logger.log(`User ${id} has no inviter, skipping rewards`);
        }

        return savedUser;
      }

      // 如果不是审核通过的更新，直接保存
      const result = await this.userRepository.save(user);
      this.logger.log(`Updated user ${id}, result:`, result);
      return result;
    } catch (error) {
      this.logger.error(`Error updating user ${id}: ${error.message}`, error.stack);
      throw error;
    }
  }

  async remove(id: number) {
    const user = await this.findOne(id);
    if (!user) {
      throw new NotFoundException(`user ${id} 没有找到!`);
    }
    return this.userRepository.remove(user);
  }

  async isLogin(user) {
    return await this.userRepository
      .createQueryBuilder('user')
      .where('user.id = :id', { id: user.id })
      .andWhere('user.loginout = :loginout', { loginout: false })
      .getMany();
  }

  async getInviteStats(userId: number) {
    const user = await this.userRepository.findOne({
      where: { id: userId }
    });

    if (!user) {
      throw new NotFoundException('用户不存在');
    }

    return {
      inviteCode: user.inviteCode,
      dailyInviteCount: user.dailyInviteCount,
      totalInviteCount: user.totalInviteCount,
      stats: await this.inviteService.getInviteStats(userId)
    };
  }

  private async validateInviteCode(inviteCode: string): Promise<boolean> {
    const inviter = await this.userRepository.findOne({
      where: { inviteCode }
    });

    if (!inviter) {
      return false;
    }

    // 检查今日邀请次数
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (inviter.lastInviteCountResetDate < today) {
      // 重置每日计数
      await this.userRepository.update(inviter.id, {
        dailyInviteCount: 0,
        lastInviteCountResetDate: today
      });
      return true;
    }

    // 检查是否达到每日上限
    const dailyLimit = 10; // 可以从配置中获取
    return inviter.dailyInviteCount < dailyLimit;
  }

  async findByEmail(email: string) {
    return await this.userRepository.findOne({
      where: { email },
      select: ['id', 'email', 'password'] // 只选择需要的字段
    });
  }

  async resetPassword(dto: ResetPasswordDto) {
    try {
      this.logger.log(`Starting password reset for email: ${dto.email}`);

      // 验证邮箱验证码
      const isValidCode = await this.emailService.verifyCode(dto.email, dto.verificationCode);
      if (!isValidCode) {
        throw new HttpException('验证码无效或已过期', 401);
      }

      // 查找用户
      const user = await this.findByEmail(dto.email);
      if (!user) {
        throw new HttpException('用户不存在', 404);
      }

      // 更新密码
      await this.userRepository.update(user.id, {
        password: md5(dto.newPassword)
      });

      this.logger.log(`Successfully reset password for user ${user.id}`);
      return '密码重置成功';
    } catch (error) {
      this.logger.error(`Password reset failed: ${error.message}`);
      throw error;
    }
  }
}
