"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.h5IosBridgeInit=void 0;var h5BridgeReadyPromise,h5IosBridgeInit=function(){return h5BridgeReadyPromise||(h5BridgeReadyPromise=new Promise(function(e,r){if("undefined"!=typeof WebViewJavascriptBridge){try{WebViewJavascriptBridge.init(function(e,r){})}catch(e){return r()}return e({})}document.addEventListener("WebViewJavascriptBridgeReady",function(){if("undefined"==typeof WebViewJavascriptBridge)return r();try{WebViewJavascriptBridge.init(function(e,r){})}catch(e){return r()}return e({})},!1)})),h5BridgeReadyPromise};exports.h5IosBridgeInit=h5IosBridgeInit;var h5IosBridge=function(e,r){return h5BridgeReadyPromise||(h5BridgeReadyPromise=(0,exports.h5IosBridgeInit)()),h5BridgeReadyPromise.then(function(){var i=Object.assign({},r);return new Promise(function(r,n){if(!0===i.watch){var t=i.onSuccess;delete i.onSuccess,"undefined"!=typeof WebViewJavascriptBridge&&WebViewJavascriptBridge.registerHandler(e,function(e,r){"function"==typeof t&&t.call(null,e),r&&r({errorCode:"0",errorMessage:"success"})})}void 0!==window.WebViewJavascriptBridge&&window.WebViewJavascriptBridge.callHandler(e,Object.assign({},i),function(e){var t=e||{};"0"===t.errorCode?("function"==typeof i.onSuccess&&i.onSuccess.call(null,t.result),r(t.result)):("-1"===t.errorCode&&"function"==typeof i.onCancel?i.onCancel.call(null,t,t.errorCode):"function"==typeof i.onFail&&i.onFail.call(null,t,t.errorCode),n(t))})})})};exports.default=h5IosBridge;