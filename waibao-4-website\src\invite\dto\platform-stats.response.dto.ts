import { ApiProperty } from '@nestjs/swagger';

class DailyStat {
  @ApiProperty({ description: '日期', example: '2023-12-14' })
  date: string;

  @ApiProperty({ description: '邀请数量', example: 10 })
  invites: number;

  @ApiProperty({ description: '奖励积分', example: 150 })
  rewards: number;
}

export class PlatformStatsResponse {
  @ApiProperty({ description: '总邀请人数' })
  totalInvites: number;

  @ApiProperty({ description: '活跃邀请人数' })
  activeInviters: number;

  @ApiProperty({ description: '总奖励积分' })
  totalRewards: number;

  @ApiProperty({
    description: '每日统计数据',
    type: [DailyStat]
  })
  dailyStats: DailyStat[];
} 