import { Controller, Get, Post, Body, Query, Req, Res } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery } from '@nestjs/swagger';
import { InviteService } from './invite.service';
import { Request, Response } from 'express';
import { RequireAdmin, RequireLogin } from '../custom-decorator';

@Controller('invite')
@ApiTags('邀请管理')
export class InviteController {
  constructor(private readonly inviteService: InviteService) {}

  @Get('stats')
  @ApiOperation({ summary: '获取邀请统计' })
  async getStats(@Req() req: Request) {
    return this.inviteService.getInviteStats(req.user.id);
  }

  @Get('share')
  @RequireLogin()
  @ApiOperation({ summary: '获取分享信息' })
  async getShareInfo(@Req() req: Request) {
    const baseUrl = process.env.APP_URL || 'http://localhost:3000';
    const inviteLink = `${baseUrl}/register?code=${req.user.inviteCode}`;
    const qrCode = await this.inviteService.generateQRCode(inviteLink);

    return {
      inviteCode: req.user.inviteCode,
      inviteLink,
      qrCode,
      shareText: `邀请你加入平台`,
      sharePlatforms: {
        wechat: {
          title: '邀请',
          desc: '邀请好友加入',
          link: inviteLink,
          imgUrl: ''
        }
      }
    };
  }

  @Get('leaderboard')
  @ApiOperation({ summary: '获取邀请排行榜' })
  @ApiQuery({
    name: 'type',
    enum: ['week', 'month', 'total'],
    description: '排行榜类型'
  })
  async getLeaderboard(@Query('type') type: 'week' | 'month' | 'total') {
    return this.inviteService.getLeaderboard(type);
  }

  @Get('analytics/admin')
  @RequireAdmin()
  @ApiOperation({ summary: '获取平台邀请分析数据' })
  async getPlatformAnalytics(
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string
  ) {
    return this.inviteService.getPlatformAnalytics(startDate, endDate);
  }

  @Get('config')
  @RequireAdmin()
  @ApiOperation({ summary: '获取邀请配置' })
  async getConfig() {
    return this.inviteService.getRewardConfig();
  }
} 