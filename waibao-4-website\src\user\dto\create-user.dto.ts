import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ptional, IsString, IsBoolean } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateUserDto {
  @ApiProperty({ description: '用户名/手机号' })
  @IsString()
  username: string;

  @ApiProperty({ description: '密码' })
  @IsString()
  password: string;

  @ApiProperty({ description: '手机号' })
  @IsString()
  phone: string;

  @ApiProperty({ description: '邮箱' })
  @IsString()
  emil: string;

  @ApiProperty({ description: '姓名' })
  @IsString()
  name: string;

  @ApiProperty({ description: 'IP地址' })
  @IsString()
  ip: string;

  @ApiProperty({ description: '积分', required: false })
  @IsNumber()
  @IsOptional()
  omn: number;

  @ApiProperty({ description: '微信OpenID', required: false })
  @IsString()
  @IsOptional()
  wxOpenid: string;

  @ApiProperty({ description: '设备ID', required: false })
  @IsString()
  @IsOptional()
  deviceId?: string;

  @ApiProperty({ description: '是否已审核通过', default: false })
  @IsBoolean()
  @IsOptional()
  approved?: boolean = false;
}
