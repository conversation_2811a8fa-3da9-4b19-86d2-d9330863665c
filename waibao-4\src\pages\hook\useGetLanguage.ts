import Taro from "@tarojs/taro";
import { useEffect } from "react";
import ZH_CN from "@/mock/zh_CN.json";
import English from "@/mock/English.json";

export default function useGetLanguage(pageTitle: string) {
  let languageValue: string = Taro.getStorageSync("language");
  let languageJson = languageValue === "zh_CN" ? ZH_CN : English;
  useEffect(() => {
    Taro.setNavigationBarTitle({
      title: languageJson[pageTitle],
    });
  }, [languageValue]);
  return languageJson;
}
