import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';

@Entity('reward_config')
export class RewardConfig {
  @PrimaryGeneratedColumn()
  id: number;

  @ApiProperty({ description: '配置名称' })
  @Column({ comment: '配置名称' })
  name: string;

  @ApiProperty({ description: '邀请人奖励积分' })
  @Column({ comment: '邀请人奖励积分', type: 'float', default: 10 })
  inviterReward: number;

  @ApiProperty({ description: '被邀请人奖励积分' })
  @Column({ comment: '被邀请人奖励积分', type: 'float', default: 5 })
  inviteeReward: number;

  @ApiProperty({ description: '任务创建奖励积分' })
  @Column({ comment: '任务创建奖励积分', type: 'float', default: 1 })
  taskCreatorReward: number;

  @ApiProperty({ description: '任务参与奖励积分' })
  @Column({ comment: '任务参与奖励积分', type: 'float', default: 1 })
  taskParticipantReward: number;

  @ApiProperty({ description: '任务分成比例（百分比）' })
  @Column({ comment: '任务分成比例（百分比）', type: 'float', default: 1 })
  taskCommissionRate: number;

  @ApiProperty({ description: '配置描述' })
  @Column({ comment: '配置描述', type: 'text', nullable: true })
  description: string;

  @Column({ default: 15 })
  tierPoints: number;

  @Column({ default: 50 })
  tierCondition: number;

  @Column({ default: 10 })
  dailyLimit: number;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
} 