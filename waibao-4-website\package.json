{"name": "all-decorator", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "cross-env RUNNING_ENV=prod nest start", "dev": "cross-env RUNNING_ENV=dev nest start --watch", "start:debug": "cross-env RUNNING_ENV=dev nest start --debug --watch", "start:prod": "nest build && pm2 start ecosystem.config.js --env production"}, "dependencies": {"@nestjs/axios": "^3.0.0", "@nestjs/common": "^9.4.3", "@nestjs/config": "^3.0.0", "@nestjs/core": "^9.4.3", "@nestjs/jwt": "^10.1.0", "@nestjs/mapped-types": "^1.2.2", "@nestjs/platform-express": "^9.4.3", "@nestjs/schedule": "^4.0.1", "@nestjs/serve-static": "^4.0.0", "@nestjs/swagger": "^7.0.4", "@nestjs/typeorm": "^9.0.1", "@types/nodemailer": "^6.4.17", "axios": "^1.5.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "cookie-parser": "^1.4.6", "cross-env": "^7.0.3", "dotenv": "^16.4.7", "ejs": "^3.1.9", "express-session": "^1.17.3", "googleapis": "^144.0.0", "moment": "^2.29.4", "multer": "1.4.5-lts.1", "mysql2": "^3.4.0", "nodemailer": "^6.10.0", "pg": "^8.11.0", "qrcode": "^1.5.3", "redis": "^4.6.7", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "svg-captcha": "^1.4.0", "typeorm": "^0.3.17", "webpack": "^5.88.0", "yaml": "^2.3.1"}, "devDependencies": {"@nestjs/schematics": "^9.2.0", "@nestjs/testing": "^9.4.3", "@types/express": "^4.17.17", "@types/jest": "29.5.1", "@types/multer": "^1.4.7", "@types/node": "18.16.12", "@types/qrcode": "^1.5.5", "@types/supertest": "^2.0.12", "@typescript-eslint/eslint-plugin": "^5.60.0", "@typescript-eslint/parser": "^5.60.0", "eslint": "^8.43.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "jest": "29.5.0", "prettier": "^2.8.8", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "4.2.0", "typescript": "^4.9.5"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}