import Taro from "@tarojs/taro";
import { useEffect, useState } from "react";

export default function useLogin() {
  let token = Taro.getStorageSync("token");
  
  // 不需要登录验证的页面路径
  const whiteList = [
    '/pages/part1/login/index',
    '/pages/part1/register/index',
    '/pages/part1/reset-password/index',
    '/pages/agreement/user/index',
    '/pages/agreement/privacy/index'
  ];

  useEffect(() => {
    // 获取当前页面路径
    const currentPage = Taro.getCurrentInstance().router?.path;
    
    // 如果当前页面在白名单中，不进行登录验证
    if (currentPage && whiteList.includes(currentPage)) {
      return;
    }

    Taro.request({
      url: process.env.TARO_APP_HOST+"/api/user/islogin",
      method: "GET",
      header: {
        token,
      },
    }).then((res) => {
      if (!res.data.success) {
        Taro.redirectTo({
          url: "pages/part1/login/index",
        });
      }
      Taro.setStorageSync("omn", res.data.data.onm);
    });
  },[]);
  return token;
}
