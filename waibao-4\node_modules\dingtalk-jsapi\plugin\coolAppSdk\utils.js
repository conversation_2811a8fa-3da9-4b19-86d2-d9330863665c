"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.isMobile=void 0;var env_1=require("../../lib/env"),dingtalkEnv=(0,env_1.getENV)(),isAndroidDingTalk=function(){return dingtalkEnv.platform===env_1.ENV_ENUM.android},isIOSDingTalk=function(){return dingtalkEnv.platform===env_1.ENV_ENUM.ios},isHarmonyDingTalk=function(){return dingtalkEnv.platform===env_1.ENV_ENUM.harmony};exports.isMobile=isIOSDingTalk()||isAndroidDingTalk()||isHarmonyDingTalk();