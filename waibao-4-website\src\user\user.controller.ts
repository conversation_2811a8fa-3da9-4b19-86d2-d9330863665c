import {
  Controller,
  Get,
  Post,
  Body,
  Inject,
  Res,
  Req,
  HttpException,
  Session,
  Param,
  BadRequestException,
  InternalServerErrorException,
} from '@nestjs/common';
import { UserService } from './user.service';
import { LoginDto } from './dto/login.dto';
import { RegisterDto } from './dto/register.dto';
import { JwtService } from '@nestjs/jwt';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { Response, Request as ExpressRequest } from 'express';
import { RequireLogin } from 'src/custom-decorator';
import { InviteStatsResponse } from '../invite/dto/invite-response.dto';
import { Request } from 'express';
import { InviteService } from '../invite/invite.service';
import { EmailService } from '../email/email.service';
import { RequestResetDto, ResetPasswordDto } from './dto/reset-password.dto';

@Controller('user')
@ApiTags('用户登录信息')
export class UserController {
  constructor(
    private readonly userService: UserService, 
    private readonly inviteService: InviteService, 
    private readonly emailService: EmailService
  ) {}

  @Inject(JwtService)
  private jwtService: JwtService;

  @RequireLogin()
  @Get('islogin')
  isLogin(@Req() req: ExpressRequest) {
    return {
      id: req.user.id,
      username: req.user.username,
      onm: req.user.omn,
    };
  }

  @Post('login')
  @ApiOperation({ summary: '用户登录' })
  async login(@Body() user: LoginDto) {
    const result = await this.userService.login(user);
    return result; // 直接返回 { token }
  }

  @Post('wx-login')
  @ApiOperation({ summary: '微信登录' })
  async wxLogin(@Body() user: LoginDto) {
    // 微信登录也使用相同的 token 机制
    const result = await this.userService.login(user);
    return result;
  }

  @Post('register')
  @ApiOperation({
    summary: '用户注册',
    description: '通过手机号注册新用户，支持邀请码'
  })
  @ApiResponse({
    status: 201,
    description: '注册成功',
    type: String
  })
  @ApiResponse({
    status: 400,
    description: '注册失败',
    schema: {
      example: {
        statusCode: 400,
        message: '手机号码长度不正确',
        error: 'Bad Request'
      }
    }
  })
  async register(
    @Body() user: RegisterDto, 
    @Req() req: ExpressRequest,
    @Session() session
  ) {
    const ip = req.ip || req.socket?.remoteAddress;
    const deviceId = req.headers['device-id'] as string || 'unknown';
    return await this.userService.register(user, ip, deviceId);
  }

  @Get('loginout')
  @RequireLogin()
  loginout(@Req() req: ExpressRequest) {
    return this.userService.loginout(req.user);
  }

  @Get('invite-link')
  @RequireLogin()
  @ApiOperation({
    summary: '获取邀请链接',
    description: '获取当前用户的邀请码和邀请链接'
  })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    schema: {
      example: {
        inviteCode: 'ABC123XY',
        inviteLink: 'https://example.com/register?code=ABC123XY',
        qrCode: 'base64...'
      }
    }
  })
  async getInviteLink(@Req() req: Request) {
    // 获取完整的用户信息
    const user = await this.userService.findOne(req.user.id);
    if (!user || !user.inviteCode) {
      throw new BadRequestException('用户信息或邀请码不存在');
    }
    
    // 使用配置的域名，这里需要根据实际情况修改
    const inviteLink = `https://www.klow2.com/?code=${user.inviteCode}#/pages/part1/register/index`;
    
    // 生成二维码
    const qrCode = await this.inviteService.generateQRCode(inviteLink);
    if (!qrCode) {
      throw new InternalServerErrorException('生成二维码失败');
    }

    return {
      inviteCode: user.inviteCode,
      inviteLink,
      qrCode
    };
  }

  @Get('invite-stats')
  @RequireLogin()
  @ApiOperation({
    summary: '获取邀请统计',
    description: '获取当前用户的邀请统计数据'
  })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    type: InviteStatsResponse
  })
  async getInviteStats(@Req() req: ExpressRequest) {
    return this.userService.getInviteStats(req.user.id);
  }

  @Get('me')
  @RequireLogin()
  async getCurrentUser(@Req() req: Request) {
    const user = await this.userService.findOne(req.user.id);
    return {
      ...user,
      password: undefined
    };
  }

  @Post('send-verification')
  @ApiOperation({ summary: '发送邮箱验证码' })
  async sendVerificationCode(@Body('email') email: string) {
    return this.emailService.sendVerificationCode(email);
  }

  @Post('request-reset-password')
  @ApiOperation({ summary: '请求重置密码' })
  async requestResetPassword(@Body() dto: RequestResetDto) {
    const user = await this.userService.findByEmail(dto.email);
    if (!user) {
      throw new HttpException('用户不存在', 404);
    }

    // 生成6位验证码
    const code = Math.random().toString().slice(2,8);
    
    // 发送重置密码邮件
    const sent = await this.emailService.sendResetPasswordEmail(dto.email, code);
    
    if (!sent) {
      throw new HttpException('邮件发送失败', 500);
    }

    return '重置密码邮件已发送，请查收';
  }

  @Post('reset-password')
  @ApiOperation({ summary: '重置密码' })
  async resetPassword(@Body() dto: ResetPasswordDto) {
    return this.userService.resetPassword(dto);
  }
}
