import { View } from "@tarojs/components";

import useGetLanguage from "@/pages/hook/useGetLanguage";
import "./index.scss";

export default function Index() {
  let language = useGetLanguage("Privacy clause");
  return (
    <View className="p-5 text-sm">
      {/* <View className=" text-2xl font-bold mb-2 text-center">网站隐私条款</View> */}
      <View className="text-lg font-bold my-1">1. {language["Information collection"]}</View>
      {language["privacy clause part1"]}
      <View className="text-lg font-bold my-1">2. {language["Use of information"]}</View>
      {language["privacy clause part2"]}
      <View className="text-lg font-bold my-1">3. {language["Information sharing"]}</View>
      {language["privacy clause part3"]}
      <View className="text-lg font-bold my-1">4. {language["Safety precautions"]}</View>
      {language["privacy clause part4"]}
      <View className="text-lg font-bold my-1">5. {language["Other"]}</View>
      {language["privacy clause part5"]}
    </View>
  );
}
