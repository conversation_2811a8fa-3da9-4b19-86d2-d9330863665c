import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Req,
  Request,
  Query,
} from '@nestjs/common';
import { BroadcastroomService } from './broadcastroom.service';
import { CreateBroadcastroomDto } from './dto/create-broadcastroom.dto';
import { UpdateBroadcastroomDto } from './dto/update-broadcastroom.dto';
import { SearchApprovedTaskDto } from './dto/search-task.dto';
import { ApiOperation, ApiTags, ApiBody, ApiResponse } from '@nestjs/swagger';
import { LoginGuard } from 'src/login.guard';
import { RequireLogin } from 'src/custom-decorator';
import { Request as ExpressRequest } from 'express';
@Controller('broadcastroom')
@ApiTags('任务管理')
@RequireLogin()
export class BroadcastroomController {
  constructor(
    private readonly broadcastroomService: BroadcastroomService,
    ) {}

  @Get()
  @ApiOperation({ summary: '全部任务', description: '查询全部的任务信息' })
  findAll(@Req() request: ExpressRequest) {
    return this.broadcastroomService.findAll();
  }

  @Get('name')
  @ApiOperation({
    summary: '我的任务',
    description: '查询我的任务',
  })
  findAllName(@Req() request: ExpressRequest) {
    return this.broadcastroomService.findAllName(request);
  }

  @Post()
  @ApiOperation({ summary: '创建任务', description: '创建新的任务信息' })
  @ApiBody({
    type: CreateBroadcastroomDto,
    description: '任务创建参数',
    examples: {
      example1: {
        value: {
          title: '示例任务',
          address: '北京市朝阳区',
          startTime: '2024-03-20 09:00:00',
          endTime: '2024-03-20 18:00:00',
          content: '这是一个示例任务的详细内容描述...',
          show: 0
        }
      }
    }
  })
  @ApiResponse({
    status: 201,
    description: '任务创建成功',
    schema: {
      example: {
        code: 200,
        data: {
          id: 1,
          title: '示例任务',
          address: '北京市朝阳区',
          startTime: '2024-03-20 09:00:00',
          endTime: '2024-03-20 18:00:00',
          content: '这是一个示例任务的详细内容描述...',
          show: 0,
          // ... other fields
        },
        message: '创建成功'
      }
    }
  })
  create(
    @Body() createBroadcastroomDto: CreateBroadcastroomDto,
    @Req() request: ExpressRequest
  ) {
    return this.broadcastroomService.create(createBroadcastroomDto, request);
  }

  @Get(':id')
  @ApiOperation({ summary: '查询任务id', description: '查询的任务信息' })
  // @RequirePermission('查询任务')
  findOne(@Param('id') id: string) {
    return this.broadcastroomService.findOne(+id);
  }

  @Patch(':id')
  // @RequirePermission('更新任务')
  @ApiOperation({ summary: '更新任务', description: '更新任务信息' })
  update(
    @Param('id') id: string,
    @Body() updateBroadcastroomDto: UpdateBroadcastroomDto,
  ) {
    return this.broadcastroomService.update(+id, updateBroadcastroomDto);
  }

  @Delete(':id')
  // @RequirePermission('删除任务')
  @ApiOperation({ summary: '删除任务', description: '删除任务信息' })
  remove(@Param('id') id: string) {
    return this.broadcastroomService.remove(+id);
  }

  @Get('search/approved')
  @ApiOperation({
    summary: '搜索已通过任务',
    description: '支持按关键词、地址、时间范围等条件搜索已通过的任务，并提供分页功能'
  })
  @ApiResponse({
    status: 200,
    description: '搜索成功',
    schema: {
      example: {
        data: [
          {
            id: 1,
            title: '示例任务',
            address: '北京市朝阳区',
            startTime: '2024-03-20 09:00:00',
            endTime: '2024-03-20 18:00:00',
            content: '任务描述',
            show: 1,
            status: '未结束',
            participantCount: 5
          }
        ],
        meta: {
          total: 100,
          page: 1,
          limit: 10,
          totalPages: 10,
          hasNextPage: true,
          hasPrevPage: false
        }
      }
    }
  })
  searchApprovedTasks(@Query() searchDto: SearchApprovedTaskDto) {
    return this.broadcastroomService.searchApprovedTasks(searchDto);
  }
}
