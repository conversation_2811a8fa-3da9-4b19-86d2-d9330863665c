import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BroadcastroomService } from './broadcastroom.service';
import { BroadcastroomController } from './broadcastroom.controller';
import { Broadcastroom } from './entities/broadcastroom.entity';
import { InviteModule } from '../invite/invite.module';
import { User } from '../user/entities/user.entity';
import { InviteRecord } from '../invite/entities/invite.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Broadcastroom,
      User,
      InviteRecord
    ]),
    InviteModule
  ],
  controllers: [BroadcastroomController],
  providers: [BroadcastroomService],
})
export class BroadcastroomModule {}
