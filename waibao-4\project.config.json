{"miniprogramRoot": "dist/", "projectname": "scenic", "description": "小程序", "appid": "wx6dcb91ec1eb44f71", "setting": {"urlCheck": true, "es6": true, "enhance": true, "compileHotReLoad": false, "postcss": false, "minified": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "packNpmRelationList": [], "uglifyFileName": false}, "compileType": "miniprogram", "libVersion": "3.1.1", "srcMiniprogramRoot": "dist/", "packOptions": {"ignore": [], "include": []}, "condition": {}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}}