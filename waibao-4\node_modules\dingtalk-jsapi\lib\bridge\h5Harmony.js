"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.h5HarmonyBridgeInit=void 0;var h5BridgeReadyPromise,h5HarmonyBridgeInit=function(){return h5BridgeReadyPromise||(h5BridgeReadyPromise=new Promise(function(e,n){if("undefined"!=typeof DingTalkJSBridge){try{DingTalkJSBridge.init(function(e,n){})}catch(e){return n()}return e({})}document.addEventListener("DingTalkJSBridgeReady",function(){if("undefined"==typeof DingTalkJSBridge)return n();try{DingTalkJSBridge.init(function(e,n){})}catch(e){return n()}return e({})},!1)})),h5BridgeReadyPromise};exports.h5HarmonyBridgeInit=h5HarmonyBridgeInit;var h5HarmonyBridge=function(e,n){return h5BridgeReadyPromise||(h5BridgeReadyPromise=(0,exports.h5HarmonyBridgeInit)()),h5BridgeReadyPromise.then(function(){return new Promise(function(i,r){var o=function(e){e.success?(!function(e){"function"==typeof n.success?n.success(e):"function"==typeof n.onSuccess&&n.onSuccess(e)}(e.body),i(e.body)):(!function(e){"function"==typeof n.fail?n.fail(e):"function"==typeof n.onFail&&n.onFail(e)}(e.body),r(e.body))};"function"==typeof window.DingTalkJSBridge.call&&window.DingTalkJSBridge.call(e,n,o)})})};exports.default=h5HarmonyBridge;