import { Button, Input, View, Image, Form } from "@tarojs/components";
import Taro from "@tarojs/taro";
import banner from "@/images/login.jpg";
import useGetLanguage from "@/pages/hook/useGetLanguage";
import { useState } from "react";
import "./index.scss";

export default function Index() {
  const language = useGetLanguage("login");
  const [step, setStep] = useState(1); // 1: 输入邮箱, 2: 输入验证码和新密码
  const [email, setEmail] = useState("");
  const [sendingCode, setSendingCode] = useState(false);
  const [countdown, setCountdown] = useState(0);

  // 发送验证码
  const sendVerificationCode = () => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return Taro.showToast({
        title: language["Invalid email format"] || "请输入正确的邮箱格式",
        icon: "none",
        duration: 2000,
      });
    }

    setSendingCode(true);
    Taro.request({
      url: '/api/user/request-reset-password',
      method: "POST",
      data: { email },
    })
      .then((res) => {
        if (res.data.success) {
          Taro.showToast({
            title: language["Code sent"] || "验证码已发送",
            icon: "success",
            duration: 2000,
          });
          setStep(2);
          // 开始倒计时
          setCountdown(60);
          const timer = setInterval(() => {
            setCountdown((prev) => {
              if (prev <= 1) {
                clearInterval(timer);
                setSendingCode(false);
                return 0;
              }
              return prev - 1;
            });
          }, 1000);
        } else {
          setSendingCode(false);
          Taro.showToast({
            title: res.data.message || language["Send failed"] || "发送失败",
            icon: "none",
            duration: 2000,
          });
        }
      })
      .catch((err) => {
        setSendingCode(false);
        Taro.showToast({
          title: err.data?.message || language["Send failed"] || "发送失败",
          icon: "none",
          duration: 2000,
        });
      });
  };

  // 重置密码
  const resetPassword = (e) => {
    const verificationCode = e.detail.value?.verificationCode;
    const newPassword = e.detail.value?.newPassword;
    const confirmPassword = e.detail.value?.confirmPassword;

    if (!verificationCode || !newPassword || !confirmPassword) {
      return Taro.showToast({
        title: language["Please fill in all fields"],
        icon: "none",
        duration: 2000,
      });
    }

    if (newPassword !== confirmPassword) {
      return Taro.showToast({
        title: language["Passwords do not match"],
        icon: "none",
        duration: 2000,
      });
    }

    // 添加密码强度验证
    const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{6,20}$/;
    if (!passwordRegex.test(newPassword)) {
      return Taro.showToast({
        title: language["Password format error"],
        icon: "none",
        duration: 2000,
      });
    }

    Taro.request({
      url: '/api/user/reset-password',
      method: "POST",
      data: {
        email,
        verificationCode,
        newPassword,
      },
    })
      .then((res) => {
        if (res.data.success) {
          Taro.showToast({
            title: language["Reset success"],
            icon: "success",
            duration: 2000,
          });
          setTimeout(() => {
            Taro.redirectTo({
              url: "/pages/part1/login/index",
            });
          }, 2000);
        } else {
          // 优化错误提示
          let errorMessage = language["Reset failed"];
          if (res.data.data?.message) {
            if (Array.isArray(res.data.data.message)) {
              errorMessage = res.data.data.message[0]; // 取第一条错误信息
            } else {
              errorMessage = res.data.data.message;
            }
          }
          Taro.showModal({
            title: language["Reset failed"],
            content: errorMessage,
            showCancel: false
          });
        }
      })
      .catch((err) => {
        let errorMessage = language["Reset failed"];
        if (err.data?.data?.message) {
          if (Array.isArray(err.data.data.message)) {
            errorMessage = err.data.data.message[0];
          } else {
            errorMessage = err.data.data.message;
          }
        }
        Taro.showModal({
          title: language["Reset failed"],
          content: errorMessage,
          showCancel: false
        });
      });
  };

  return (
    <View className="p-4">
      <View className="text-center py-2.5">
        <Image mode="widthFix" src={banner}></Image>
      </View>
      <View className="w-full h-full px-10 box-border">
        <View className="text-xl font-bold m-b-5 tracking-wider">
          {language["Reset Password"]}
        </View>
        
        {step === 1 ? (
          // 第一步：输入邮箱
          <View>
            <View className="flex justify-between items-center w-full">
              <View className="i-ic-baseline-email text-gray-400 text-xl"></View>
              <Input
                type="text"
                value={email}
                onInput={e => setEmail(e.detail.value)}
                className="w-full py-2 px-1 text-sm placeholder-gray-200"
                placeholder={language["Please enter your email"]}
              />
            </View>
            <View className="bg-gray-100 h-0.5 mb-4"></View>
            <Button
              className="bg-[#1659c0] text-white border-none btn"
              disabled={sendingCode}
              onClick={sendVerificationCode}
            >
              {sendingCode ? `${countdown}s` : language["Send verification code"]}
            </Button>
          </View>
        ) : (
          // 第二步：输入验证码和新密码
          <Form onSubmit={resetPassword}>
            <View className="flex justify-between items-center w-full mb-2">
              <View className="i-ic-baseline-admin-panel-settings text-gray-400 text-xl"></View>
              <Input
                type="text"
                name="verificationCode"
                className="w-4/6 py-2 px-1 text-sm placeholder-gray-200"
                placeholder={language["Please enter the verification code"]}
              />
              <View 
                className={`w-2/6 text-sm text-center ${sendingCode ? 'text-gray-400' : 'text-[#1659c0]'}`}
                onClick={!sendingCode && countdown === 0 ? sendVerificationCode : undefined}
              >
                {countdown > 0 ? `${countdown}s` : language["Send verification code"]}
              </View>
            </View>
            <View className="bg-gray-100 h-0.5 mb-2"></View>
            
            <View className="flex justify-between items-center w-full mb-2">
              <View className="i-ic-baseline-lock text-gray-400 text-xl"></View>
              <Input
                type="password"
                name="newPassword"
                className="w-full py-2 px-1 text-sm placeholder-gray-200"
                placeholder={language["Please enter new password"]}
              />
            </View>
            <View className="bg-gray-100 h-0.5 mb-2"></View>

            <View className="flex justify-between items-center w-full mb-4">
              <View className="i-ic-baseline-lock text-gray-400 text-xl"></View>
              <Input
                type="password"
                name="confirmPassword"
                className="w-full py-2 px-1 text-sm placeholder-gray-200"
                placeholder={language["Please confirm new password"]}
              />
            </View>
            <View className="bg-gray-100 h-0.5 mb-4"></View>

            <Button
              formType="submit"
              className="bg-[#1659c0] text-white border-none btn"
            >
              {language["Reset Password"]}
            </Button>
          </Form>
        )}

        <View
          className="text-center text-[#1659c0] py-5 text-xs"
          onClick={() => {
            Taro.redirectTo({
              url: "/pages/part1/login/index",
            });
          }}
        >
          {language["Back to login"]}
        </View>
      </View>
    </View>
  );
} 