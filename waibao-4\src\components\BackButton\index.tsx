import { View } from '@tarojs/components'
import { AtIcon } from 'taro-ui'
import Taro from '@tarojs/taro'

export const BackButton = () => {
  const handleBack = () => {
    const pages = Taro.getCurrentPages()
    if (pages.length > 1) {
      Taro.navigateBack()
    } else {
      Taro.switchTab({
        url: '/pages/part2/dashboard/index'
      })
    }
  }

  return (
    <View 
      style={{
        position: 'fixed',
        top: '20px',
        right: '20px',
        zIndex: 999,
        width: '40px',
        height: '40px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: 'rgba(255, 255, 255, 0.9)',
        borderRadius: '50%',
        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
      }}
      onClick={handleBack}
    >
      <View className='i-ic-baseline-arrow-back text-xl text-gray-600' />
    </View>
  )
} 