import { View, Image } from "@tarojs/components";
import { AtTabs, AtTabsPane } from "taro-ui";
import banner from "@/images/task.jpg";
import { useEffect, useState } from "react";
// import cardDatas from "@/mock/cards.json";
import useGetLanguage from "@/pages/hook/useGetLanguage";

import Card from "../card";
import "./index.scss";
import Taro from "@tarojs/taro";
import { getTime } from "@/pages/utils";

export default function Index({ token }) {
  let [current, setCurrent] = useState(0);
  let language = useGetLanguage("Home");
  let [start_datas, set_start_datas] = useState([]);
  let [end_datas, set_end_datas] = useState([]);
  useEffect(() => {
    Taro.request({
      url: process.env.TARO_APP_HOST + "/api/broadcastroom/name",
      method: "GET",
      header: {
        token,
      },
    })
      .then((res) => {
        let data = res.data.data;
        data = data.map(item=>({
          ...item,
          time:getTime(item).day
        }))
        set_end_datas(data.filter((item) => item.status == "已结束").reverse());
        set_start_datas(data.filter((item) => item.status == "未结束").reverse());
      })
      .catch((err) => {
        Taro.showToast({
          title: "查询错误！",
          icon: "none",
        });
      });
  }, []);

  const tabList = [
    { title: `${language["Ongoing task"]}(${start_datas.length})` },
    { title: `${language["Completed task"]}(${end_datas.length})` },
  ];
  return (
    <View>
      <View className="text-center relative ">
        <Image mode="widthFix" className="w-full" src={banner}></Image>
      </View>
      <View className=" w-full rounded-2xl bg-white relative -top-35 overflow-hidden">
        <AtTabs
          current={current}
          tabList={tabList}
          onClick={(cur) => {
            setCurrent(cur);
          }}
        >
          <AtTabsPane current={current} index={0}>
            <View className="bg-gray-200 pt-3 pb-50">
              {start_datas.map((item) => (
                <Card key={item.id} item={item}></Card>
              ))}
            </View>
          </AtTabsPane>
          <AtTabsPane current={current} index={1}>
            <View className="bg-gray-200 pt-3 pb-50">
              {end_datas.map((item) => (
                <Card key={item.id} item={item}></Card>
              ))}
            </View>
          </AtTabsPane>
        </AtTabs>
      </View>
    </View>
  );
}
