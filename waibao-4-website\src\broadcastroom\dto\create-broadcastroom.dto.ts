import { IsNumber, IsString, IsOptional } from "class-validator";
import { ApiProperty } from '@nestjs/swagger';

export class CreateBroadcastroomDto {

    @ApiProperty({ 
        description: '任务标题',
        example: '示例任务'
    })
    @IsString()
    title: string;

    @ApiProperty({ 
        description: '活动地址',
        example: '北京市朝阳区'
    })
    @IsString()
    address: string;

    @ApiProperty({ 
        description: '开始时间',
        example: '2024-03-20 09:00:00'
    })
    @IsString()
    startTime: string;

    @ApiProperty({ 
        description: '结束时间',
        example: '2024-03-20 18:00:00'
    })
    @IsString()
    endTime: string;

    @ApiProperty({ 
        description: '审核状态',
        example: 0,
        default: 0
    })
    @IsNumber()
    show: number;

    @ApiProperty({ 
        description: '任务内容',
        example: '这是一个示例任务的详细内容描述...',
        required: true 
    })
    @IsString()
    content: string;
    
}
