import { View, Text, Image, Input, <PERSON><PERSON>, Picker } from "@tarojs/components";
import { useState, useEffect } from "react";
import Taro from "@tarojs/taro";
import { AtTabs, AtTabsPane } from "taro-ui";
import "taro-ui/dist/style/components/tabs.scss";
import "taro-ui/dist/style/components/tab-bar.scss";

interface User {
  id: number;
  username: string;
  phone: string;
  email: string;
  name: string;
  createTime: string;
  updateTime: string;
  omn: number;
  ip: string;
  loginout: Record<string, any>;
  wxOpenid: string;
  broadcastrooms: string[];
  approved: boolean;
  totalInviteCount?: number;
  dailyInviteCount?: number;
}

interface Task {
  id: number;
  title: string;
  address: string;
  startTime: string;
  endTime: string;
  phone: string;
  status: string;
  author: number;
  show: number;
  users: User[];
}

interface Invitation {
  id: number;
  code: string;
  status: string;
  createTime: string;
  updateTime: string;
  userId?: number;
  usedTime?: string;
}

// 添加排行榜相关的接口定义
interface LeaderboardUser {
  id: number;
  username: string;
  name: string;
}

interface LeaderboardItem {
  user: LeaderboardUser;
  inviteCount: number;
  totalPoints: number;
}

interface LeaderboardMeta {
  startDate: string;
  endDate: string;
  type: "week" | "month" | "total";
}

interface LeaderboardData {
  data: LeaderboardItem[];
  total: number;
  meta: LeaderboardMeta;
}

// 添加分享相关的接口定义
interface SharePlatform {
  title: string;
  desc: string;
  link: string;
  imgUrl: string;
}

interface ShareInfo {
  inviteCode: string;
  inviteLink: string;
  qrCode: string;
  shareText: string;
  sharePlatforms: {
    wechat: SharePlatform;
  };
}

// 添加分析数据相关的接口定义
interface DailyTrend {
  date: string;
  count: number;
}

interface DailyStat {
  date: string;
  invites: number;
  rewards: number;
}

interface AdminAnalytics {
  totalInvites: number;
  activeInviters: number;
  totalRewards: number;
  dailyStats: DailyStat[];
}

// 添加邀请配置相关的接口定义
interface RewardConfig {
  id: number;
  name: string;
  inviterReward: number;
  inviteeReward: number;
  taskCreatorReward: number;
  taskParticipantReward: number;
  taskCommissionRate: number;
  description: string;
  createdAt: string;
  updatedAt: string;
}

interface RewardConfigForm {
  name?: string;
  inviterReward?: number;
  inviteeReward?: number;
  taskCreatorReward?: number;
  taskParticipantReward?: number;
  taskCommissionRate?: number;
  description?: string;
}

interface InviteConfig {
  id: number;
  name: string;
  status: string;
  createdAt: string;
  updatedAt: string;
}

interface PaginatedResponse<T> {
  data: T[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

interface UserDetailResponse extends User {
  totalInviteCount: number;
  dailyInviteCount: number;
}

interface TaskDetailResponse extends Task {
  content: string;
  users: User[];
}

export default function AdminDashboard() {
  const [activeTab, setActiveTab] = useState<number>(0);
  const [currentView, setCurrentView] = useState<string>("users");
  const [users, setUsers] = useState<User[]>([]);
  const [tasks, setTasks] = useState<Task[]>([]);
  const [invitations, setInvitations] = useState<Invitation[]>([]);
  const [loading, setLoading] = useState(true);
  const [leaderboardType, setLeaderboardType] = useState<
    "week" | "month" | "total"
  >("week");
  const [leaderboard, setLeaderboard] = useState<LeaderboardData | null>(null);
  const [shareInfo, setShareInfo] = useState<ShareInfo | null>(null);
  const [exportStartDate, setExportStartDate] = useState("");
  const [exportEndDate, setExportEndDate] = useState("");
  const [analytics, setAnalytics] = useState<AdminAnalytics | null>(null);
  const [configs, setConfigs] = useState<InviteConfig[]>([]);
  const [editingConfig, setEditingConfig] = useState<InviteConfig | null>(null);
  const [rewardConfig, setRewardConfig] = useState<RewardConfig | null>(null);
  const [isEditingReward, setIsEditingReward] = useState(false);
  const [editingRewardForm, setEditingRewardForm] = useState<RewardConfigForm>(
    {}
  );
  const [allUsers, setAllUsers] = useState<User[]>([]);
  const [allTasks, setAllTasks] = useState<Task[]>([]);
  const [userPage, setUserPage] = useState(1);
  const [taskPage, setTaskPage] = useState(1);
  const [userMeta, setUserMeta] = useState<{
    total: number;
    totalPages: number;
  }>({ total: 0, totalPages: 0 });
  const [taskMeta, setTaskMeta] = useState<{
    total: number;
    totalPages: number;
  }>({ total: 0, totalPages: 0 });
  const [selectedUser, setSelectedUser] = useState<UserDetailResponse | null>(
    null
  );
  const [selectedTask, setSelectedTask] = useState<TaskDetailResponse | null>(
    null
  );
  const [showUserDetail, setShowUserDetail] = useState(false);
  const [showTaskDetail, setShowTaskDetail] = useState(false);
  const [tabGroups, setTabGroups] = useState({
    activeGroup: 0,
    groupedTabs: [
      {
        title: "审核管理",
        icon: "📝",
        tabs: [
          { title: `待审核用户`, component: 0 },
          { title: `待审核任务`, component: 1 },
        ],
      },
      {
        title: "系统设置",
        icon: "⚙️",
        tabs: [
          // { title: '邀请码管理', component: 2 },
          { title: "奖励配置", component: 3 },
        ],
      },
      {
        title: "数据管理",
        icon: "📊",
        tabs: [
          { title: "用户管理", component: 4 },
          { title: "任务管理", component: 5 },
        ],
      },
    ],
  });
  const [expandedGroups, setExpandedGroups] = useState<boolean[]>([
    true,
    false,
    false,
  ]);
  const [selectedTab, setSelectedTab] = useState<{
    groupIndex: number;
    tabIndex: number;
  }>({ groupIndex: 0, tabIndex: 0 });
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  const fetchData = () => {
    setLoading(true);
    const token = Taro.getStorageSync("adminToken");

    // 获取待审核用户
    Taro.request({
      url: `${process.env.TARO_APP_HOST}/api/admin/users`,
      header: { token },
    })
      .then((res) => {
        if (res.statusCode === 200) {
          setUsers(res.data.data || []);
        }
      })
      .catch((err) => {
        console.error("获取用户数据失败:", err);
        Taro.showToast({
          title: "获取用户数据失败",
          icon: "none",
        });
      });

    // 获取待审核任务
    Taro.request({
      url: `${process.env.TARO_APP_HOST}/api/admin/tasks`,
      header: { token },
    })
      .then((res) => {
        if (res.statusCode === 200) {
          setTasks(res.data.data || []);
        }
      })
      .catch((err) => {
        console.error("获取任务数据失败:", err);
        Taro.showToast({
          title: "获取任务数据失败",
          icon: "none",
        });
      })
      .finally(() => {
        setLoading(false);
      });

    fetchLeaderboard(leaderboardType);
    fetchShareInfo();
    fetchAnalytics();
    fetchConfigs();
  };

  // 审核用户
  const handleUserAction = (userId: number, approved: boolean) => {
    const token = Taro.getStorageSync("adminToken");
    Taro.request({
      url: `${process.env.TARO_APP_HOST}/api/admin/user/${userId}`,
      method: "PATCH",
      header: { token },
      data: { approved },
    })
      .then((res) => {
        if (res.statusCode === 200) {
          Taro.showToast({
            title: approved ? "已通过" : "已拒绝",
            icon: "success",
          });
          fetchData(); // 刷新数据
        }
      })
      .catch((err) => {
        console.error("审核用户失败:", err);
        Taro.showToast({
          title: "操作失败",
          icon: "none",
        });
      });
  };

  // 审核任务
  const handleTaskAction = (taskId: number, approved: boolean) => {
    const token = Taro.getStorageSync("adminToken");
    Taro.request({
      url: `${process.env.TARO_APP_HOST}/api/admin/task/${taskId}`,
      method: "PATCH",
      header: { token },
      data: { approved },
    })
      .then((res) => {
        if (res.statusCode === 200) {
          Taro.showToast({
            title: approved ? "已通过" : "已拒绝",
            icon: "success",
          });
          fetchData(); // 刷新数据
        }
      })
      .catch((err) => {
        console.error("审核任务失败:", err);
        Taro.showToast({
          title: "操作失败",
          icon: "none",
        });
      });
  };

  // 获取排行榜数据的函数
  const fetchLeaderboard = (type: "week" | "month" | "total") => {
    const token = Taro.getStorageSync("adminToken");
    Taro.request({
      url: `${process.env.TARO_APP_HOST}/api/invite/leaderboard`,
      header: { token },
      data: { type },
    })
      .then((res) => {
        if (res.statusCode === 200) {
          setLeaderboard(res.data);
        }
      })
      .catch((err) => {
        console.error("获取排行榜数据失败:", err);
        Taro.showToast({
          title: "获取排行榜数据失败",
          icon: "none",
        });
      });
  };

  // 获取分享信息的函数
  const fetchShareInfo = () => {
    const token = Taro.getStorageSync("adminToken");
    Taro.request({
      url: `${process.env.TARO_APP_HOST}/api/invite/share`,
      header: { token },
    })
      .then((res) => {
        if (res.statusCode === 200) {
          setShareInfo(res.data);
        }
      })
      .catch((err) => {
        console.error("获取分享信息失败:", err);
        Taro.showToast({
          title: "获取分享信息失败",
          icon: "none",
        });
      });
  };

  // 添加导出函数
  const handleExport = () => {
    const token = Taro.getStorageSync("adminToken");
    Taro.downloadFile({
      url: `${process.env.TARO_APP_HOST}/api/invite/export?startDate=${exportStartDate}&endDate=${exportEndDate}`,
      header: { token },
      success: (res) => {
        if (res.statusCode === 200) {
          // 保存文件
          Taro.saveFile({
            tempFilePath: res.tempFilePath,
            success: (saveRes) => {
              Taro.showToast({
                title: "导出成功",
                icon: "success",
              });
            },
          });
        }
      },
      fail: () => {
        Taro.showToast({
          title: "导出失败",
          icon: "none",
        });
      },
    });
  };

  // 添加获取分析数据的函数
  const fetchAnalytics = () => {
    const token = Taro.getStorageSync("adminToken");
    Taro.request({
      url: `${process.env.TARO_APP_HOST}/api/invite/analytics/admin`,
      header: { token },
      data: {
        startDate: exportStartDate || undefined,
        endDate: exportEndDate || undefined,
      },
    })
      .then((res) => {
        if (res.statusCode === 200) {
          setAnalytics(res.data);
        }
      })
      .catch((err) => {
        console.error("获取分析数据失败:", err);
        Taro.showToast({
          title: "获取分析数据失败",
          icon: "none",
        });
      });
  };

  // 获取配置的函数
  const fetchConfigs = () => {
    const token = Taro.getStorageSync("adminToken");
    Taro.request({
      url: `${process.env.TARO_APP_HOST}/api/invite/config`,
      header: { token },
    })
      .then((res) => {
        if (res.statusCode === 200) {
          // 确保的数据是数组
          setConfigs(Array.isArray(res.data) ? res.data : []);
        }
      })
      .catch((err) => {
        console.error("获取配置失败:", err);
        Taro.showToast({
          title: "获取配置失败",
          icon: "none",
        });
      });
  };

  // 切换配置启用状态的函数
  const handleToggleConfig = (id: number) => {
    const token = Taro.getStorageSync("adminToken");
    Taro.request({
      url: `${process.env.TARO_APP_HOST}/api/invite/config/toggle/${id}`,
      method: "POST",
      header: { token },
    })
      .then((res) => {
        if (res.statusCode === 200) {
          Taro.showToast({
            title: "切换成功",
            icon: "success",
          });
          fetchConfigs();
        }
      })
      .catch((err) => {
        console.error("切换配置失败:", err);
        Taro.showToast({
          title: "操作失败",
          icon: "none",
        });
      });
  };

  // 获取奖励配置
  const fetchRewardConfig = () => {
    const token = Taro.getStorageSync("adminToken");
    Taro.request({
      url: `${process.env.TARO_APP_HOST}/api/reward-config`,
      header: { token },
    })
      .then((res) => {
        if (res.statusCode === 200 && res.data.success) {
          setRewardConfig(res.data.data);
        }
      })
      .catch((err) => {
        console.error("获取奖励配置失败:", err);
        Taro.showToast({
          title: "获取奖励配置失败",
          icon: "none",
        });
      });
  };

  // 验证奖励配置表单
  const validateRewardForm = (form: RewardConfigForm): string | null => {
    if (form.inviterReward !== undefined && form.inviterReward < 1) {
      return "邀请人奖励必须大于等于1";
    }
    if (form.inviteeReward !== undefined && form.inviteeReward < 1) {
      return "被邀请人奖励必须大于等于1";
    }
    if (form.taskCreatorReward !== undefined && form.taskCreatorReward < 1) {
      return "任务发布者奖励必须大于等于1";
    }
    if (
      form.taskParticipantReward !== undefined &&
      form.taskParticipantReward < 1
    ) {
      return "任务参与者奖励必须大于等于1";
    }
    if (
      form.taskCommissionRate !== undefined &&
      (form.taskCommissionRate < 1 || form.taskCommissionRate > 100)
    ) {
      return "任务分成比例必须在1-100之间";
    }
    return null;
  };

  // 更新奖励配置
  const handleUpdateRewardConfig = () => {
    const error = validateRewardForm(editingRewardForm);
    if (error) {
      Taro.showToast({
        title: error,
        icon: "none",
      });
      return;
    }

    // 只提需要的字段
    const requestData = {
      name: editingRewardForm.name,
      inviterReward: editingRewardForm.inviterReward,
      inviteeReward: editingRewardForm.inviteeReward,
      taskCreatorReward: editingRewardForm.taskCreatorReward,
      taskParticipantReward: editingRewardForm.taskParticipantReward,
      taskCommissionRate: editingRewardForm.taskCommissionRate,
      description: editingRewardForm.description,
    };

    const token = Taro.getStorageSync("adminToken");
    Taro.request({
      url: `${process.env.TARO_APP_HOST}/api/reward-config/update`,
      method: "PUT",
      header: { token },
      data: requestData,
    })
      .then((res) => {
        if (res.statusCode === 200) {
          Taro.showToast({
            title: "更新成功",
            icon: "success",
          });
          setIsEditingReward(false);
          fetchRewardConfig();
        }
      })
      .catch((err) => {
        console.error("更新奖励配置失败:", err);
        Taro.showToast({
          title: "更新失败",
          icon: "none",
        });
      });
  };

  const fetchAllUsers = (page = 1, limit = 10) => {
    const token = Taro.getStorageSync("adminToken");
    Taro.request({
      url: `${process.env.TARO_APP_HOST}/api/admin/all-users`,
      header: { token },
      data: { page, limit },
    })
      .then((res) => {
        if (res.statusCode === 200) {
          let data = res.data.data || [];
          console.log(data);
          setAllUsers(data.data || []);
          setUserMeta({
            total: data.meta.total,
            totalPages: data.meta.totalPages,
          });
        }
      })
      .catch((err) => {
        console.error("获取所有用户失败:", err);
        Taro.showToast({
          title: "获取用户数据失败",
          icon: "none",
        });
      });
  };

  const fetchAllTasks = (page = 1, limit = 10) => {
    const token = Taro.getStorageSync("adminToken");
    Taro.request({
      url: `${process.env.TARO_APP_HOST}/api/admin/all-tasks`,
      header: { token },
      data: { page, limit },
    })
      .then((res) => {
        if (res.statusCode === 200) {
          let data = res.data.data || [];
          setAllTasks(data.data || []);
          setTaskMeta({
            total: data.meta.total,
            totalPages: data.meta.totalPages,
          });
        }
      })
      .catch((err) => {
        console.error("获取所有任务失败:", err);
        Taro.showToast({
          title: "获取任务数据失败",
          icon: "none",
        });
      });
  };

  const status = (show)=>{
    if(show ==1){
      return '审核通过'
    }
    if(show == 0){
      return "等待审核"
    }
    if(show == -1){
      return "审核拒绝"
    }
  }
  const fetchUserDetail = (userId: number) => {
    const token = Taro.getStorageSync("adminToken");
    Taro.request({
      url: `${process.env.TARO_APP_HOST}/api/admin/user/${userId}`,
      header: { token },
    })
      .then((res) => {
        if (res.statusCode === 200) {
          setSelectedUser(res.data.data);
          setShowUserDetail(true);
        }
      })
      .catch((err) => {
        console.error("获取用户详情失败:", err);
        Taro.showToast({
          title: "获取用户详情失败",
          icon: "none",
        });
      });
  };

  const fetchTaskDetail = (taskId: number) => {
    const token = Taro.getStorageSync("adminToken");
    Taro.request({
      url: `${process.env.TARO_APP_HOST}/api/admin/task-detail/${taskId}`,
      header: { token },
    })
      .then((res) => {
        if (res.statusCode === 200) {
          setSelectedTask(res.data.data);
          setShowTaskDetail(true);
        }
      })
      .catch((err) => {
        console.error("获取任务详情失败:", err);
        Taro.showToast({
          title: "获取任务详情失败",
          icon: "none",
        });
      });
  };

  const handleDeleteUser = (userId: number) => {
    Taro.showModal({
      title: "确认删除",
      content: "确定要删除这个用户吗？此操作不可撤销。",
      success: (res) => {
        if (res.confirm) {
          const token = Taro.getStorageSync("adminToken");
          Taro.request({
            url: `${process.env.TARO_APP_HOST}/api/admin/user/${userId}`,
            method: "DELETE",
            header: { token },
          })
            .then((res) => {
              if (res.statusCode === 200) {
                Taro.showToast({
                  title: "删除成功",
                  icon: "success",
                });
                fetchAllUsers(userPage);
                setShowUserDetail(false);
              }
            })
            .catch((err) => {
              console.error("删除用户失败:", err);
              Taro.showToast({
                title: "删除用户失败",
                icon: "none",
              });
            });
        }
      },
    });
  };

  // 切换分组展开/折叠状态
  const toggleGroup = (groupIndex: number) => {
    const newExpandedGroups = [...expandedGroups];
    newExpandedGroups[groupIndex] = !newExpandedGroups[groupIndex];
    setExpandedGroups(newExpandedGroups);
  };

  // 处理子标签点击
  const handleTabClick = (groupIndex: number, tabIndex: number) => {
    setSelectedTab({ groupIndex, tabIndex });
    const componentIndex =
      tabGroups.groupedTabs[groupIndex].tabs[tabIndex].component;
    setActiveTab(componentIndex);

    // 加载对应的数据
    if (componentIndex === 2) {
      // 获取邀请码列表
      const token = Taro.getStorageSync("adminToken");
      Taro.request({
        url: `${process.env.TARO_APP_HOST}/api/invite/codes`,
        header: { token },
      }).then((res) => {
        if (res.statusCode === 200) {
          setInvitations(res.data.data || []);
        }
      });
    } else if (componentIndex === 3) {
      // 获取奖励配置
      fetchRewardConfig();
    } else if (componentIndex === 4) {
      fetchAllUsers();
    } else if (componentIndex === 5) {
      fetchAllTasks();
    }
  };

  // 切换侧边栏展开/收起状态
  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  useEffect(() => {
    const token = Taro.getStorageSync("adminToken");
    if (!token) {
      Taro.redirectTo({ url: "/pages/admin/index" });
      return;
    }
    fetchData();
    fetchRewardConfig();

    // 根据当前活动标签加载数据
    if (activeTab === 2) {
      // 加载邀请码数据
      Taro.request({
        url: `${process.env.TARO_APP_HOST}/api/invite/codes`,
        header: { token },
      }).then((res) => {
        if (res.statusCode === 200) {
          setInvitations(res.data.data || []);
        }
      });
    } else if (activeTab === 4) {
      fetchAllUsers();
    } else if (activeTab === 5) {
      fetchAllTasks();
    }
  }, [activeTab]);

  return (
    <View className="flex flex-col md:flex-row h-screen">
      {/* 移动端顶部导航 */}
      <View className="md:hidden bg-gray-100 p-4 flex items-center justify-between">
        <Text className="font-bold text-lg">管理员面板</Text>
        <View
          className="p-2 rounded bg-white"
          onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
        >
          <Text>{sidebarCollapsed ? "菜单 ☰" : "关闭 ✕"}</Text>
        </View>
      </View>

      {/* 左侧导航栏 - 移动端时为弹出层 */}
      <View
        className={`bg-gray-100 transition-all duration-300 ${
          sidebarCollapsed
            ? "md:w-16 hidden md:block"
            : "md:w-52 fixed md:relative inset-0 z-40 md:z-auto"
        } md:h-screen overflow-y-auto`}
      >
        {/* 关闭按钮 - 仅移动端显示 */}
        <View
          className="md:hidden p-4 flex justify-end"
          onClick={() => setSidebarCollapsed(true)}
        >
          <Text className="text-2xl">✕</Text>
        </View>

        {/* 折叠按钮 - 仅桌面端显示 */}
        <View
          className="hidden md:block absolute -right-3 top-4 bg-white w-6 h-6 rounded-full shadow flex items-center justify-center cursor-pointer z-10"
          onClick={toggleSidebar}
        >
          <Text>{sidebarCollapsed ? "→" : "←"}</Text>
        </View>

        <View
          className={`p-4 ${
            sidebarCollapsed ? "items-center" : ""
          } flex flex-col`}
        >
          <View
            className={`hidden md:block font-bold mb-6 ${
              sidebarCollapsed ? "text-center text-sm" : "text-xl"
            }`}
          >
            {sidebarCollapsed ? "管理" : "管理员面板"}
          </View>

          {/* 分组菜单 */}
          <View className="flex-1 overflow-y-auto">
            {tabGroups.groupedTabs.map((group, groupIndex) => (
              <View key={groupIndex} className="mb-2">
                {/* 分组标题 */}
                <View
                  className={`flex items-center p-3 bg-white rounded cursor-pointer hover:bg-gray-200 ${
                    sidebarCollapsed ? "justify-center" : ""
                  }`}
                  onClick={() =>
                    sidebarCollapsed &&
                    Taro.getSystemInfoSync().windowWidth >= 768
                      ? handleTabClick(groupIndex, 0)
                      : toggleGroup(groupIndex)
                  }
                >
                  <Text className={`${!sidebarCollapsed ? "mr-2" : ""}`}>
                    {group.icon}
                  </Text>
                  {!sidebarCollapsed && (
                    <>
                      <Text className="flex-1 font-medium">{group.title}</Text>
                      <Text>{expandedGroups[groupIndex] ? "▲" : "▼"}</Text>
                    </>
                  )}
                </View>

                {/* 子菜单 - 仅在展开状态下显示 */}
                {!sidebarCollapsed && expandedGroups[groupIndex] && (
                  <View className="pl-4 mt-1 space-y-1">
                    {group.tabs.map((tab, tabIndex) => (
                      <View
                        key={tabIndex}
                        className={`p-2 pl-6 rounded cursor-pointer ${
                          selectedTab.groupIndex === groupIndex &&
                          selectedTab.tabIndex === tabIndex
                            ? "bg-blue-500 text-white"
                            : "bg-gray-50 hover:bg-gray-200"
                        }`}
                        onClick={() => {
                          handleTabClick(groupIndex, tabIndex);
                          // 在移动端选择选项后自动关闭侧边栏
                          if (Taro.getSystemInfoSync().windowWidth < 768) {
                            setSidebarCollapsed(true);
                          }
                        }}
                      >
                        <Text>{tab.title}</Text>
                      </View>
                    ))}
                  </View>
                )}
              </View>
            ))}
          </View>

          {/* 退出登录按钮 */}
          <View
            className={`mt-6 p-3 bg-red-100 text-red-600 rounded text-center cursor-pointer hover:bg-red-200 ${
              sidebarCollapsed ? "px-2 py-2" : ""
            }`}
            onClick={() => {
              Taro.clearStorageSync();
              Taro.redirectTo({ url: "/pages/admin/index" });
            }}
          >
            <Text>{sidebarCollapsed ? "退" : "退出登录"}</Text>
          </View>
        </View>
      </View>

      {/* 页面遮罩 - 移动端侧边栏打开时显示 */}
      {!sidebarCollapsed && Taro.getSystemInfoSync().windowWidth < 768 && (
        <View
          className="fixed inset-0 bg-black bg-opacity-50 z-30"
          onClick={() => setSidebarCollapsed(true)}
        />
      )}

      {/* 右侧内容区 */}
      <View className="flex-1 p-3 md:p-6 overflow-y-auto h-screen">
        {/* 顶部面包屑 */}
        <View className="flex items-center mb-4 md:mb-6 text-gray-500">
          <Text>{tabGroups.groupedTabs[selectedTab.groupIndex].title}</Text>
          <Text className="mx-2">›</Text>
          <Text className="font-medium text-gray-800">
            {
              tabGroups.groupedTabs[selectedTab.groupIndex].tabs[
                selectedTab.tabIndex
              ].title
            }
          </Text>
        </View>

        {/* 内容面板 */}
        <View className="bg-white p-3 md:p-6 rounded-lg shadow">
          {activeTab === 0 &&
            (loading ? (
              <View className="text-center py-4">
                <Text>加载中...</Text>
              </View>
            ) : (
              <View className="space-y-4">
                {users.map((user) => (
                  <View key={user.id} className="bg-white p-4 border rounded">
                    <View className="mb-2">用户名: {user.username}</View>
                    <View className="mb-2">手机: {user.phone}</View>
                    <View className="mb-2">邮箱: {user.email}</View>
                    <View className="mb-2">姓名: {user.name}</View>
                    <View className="mb-2">
                      注册时间: {new Date(user.createTime).toLocaleString()}
                    </View>
                    <View className="mb-2">IP地址: {user.ip}</View>
                    <View className="flex justify-end space-x-4">
                      <Text
                        className="text-green-500 cursor-pointer text-sm"
                        onClick={() => handleUserAction(user.id, true)}
                      >
                        通过
                      </Text>
                      <Text
                        className="text-red-500 cursor-pointer text-sm"
                        onClick={() => handleUserAction(user.id, false)}
                      >
                        拒绝
                      </Text>
                    </View>
                  </View>
                ))}
                {users.length === 0 && (
                  <View className="text-center text-gray-500 py-8">
                    暂无待审核用户
                  </View>
                )}
              </View>
            ))}

          {activeTab === 1 &&
            (loading ? (
              <View className="text-center py-4">
                <Text>加载中...</Text>
              </View>
            ) : (
              <View className="space-y-4">
                {tasks.map((task) => (
                  <View key={task.id} className="bg-white p-4 border rounded">
                    <View className="mb-2">标题: {task.title}</View>
                    <View className="mb-2">地址: {task.address}</View>
                    <View className="mb-2">开始时间: {task.startTime}</View>
                    <View className="mb-2">结束时间: {task.endTime}</View>
                    <View className="mb-2">联系方式: {task.phone}</View>
                    <View className="mb-2">状态: {task.status}</View>
                    <View className="mb-2">内容: {task.content}</View>
                    <View className="mb-2">发布者ID: {task.author}</View>
                    <View className="flex justify-end space-x-4">
                      <Text
                        className="text-green-500 cursor-pointer text-sm"
                        onClick={() => handleTaskAction(task.id, true)}
                      >
                        通过
                      </Text>
                      <Text
                        className="text-red-500 cursor-pointer text-sm"
                        onClick={() => handleTaskAction(task.id, false)}
                      >
                        拒绝
                      </Text>
                    </View>
                  </View>
                ))}
                {tasks.length === 0 && (
                  <View className="text-center text-gray-500 py-8">
                    暂无待审核任务
                  </View>
                )}
              </View>
            ))}

          {activeTab === 2 && (
            <View className="space-y-4">
              {/* 邀请数据分析 */}
              {analytics && (
                <View className="bg-white p-4 rounded-lg border mb-6">
                  <Text className="text-lg font-bold mb-4">邀请数据分析</Text>

                  {/* 数据概览 - 移动端垂直排列，桌面端水平排列 */}
                  <View className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                    <View className="bg-blue-50 p-4 rounded-lg">
                      <Text className="text-sm text-gray-500">总邀请数</Text>
                      <Text className="block text-2xl font-bold text-blue-600">
                        {analytics.totalInvites}
                      </Text>
                    </View>
                    <View className="bg-green-50 p-4 rounded-lg">
                      <Text className="text-sm text-gray-500">
                        活跃邀请人数
                      </Text>
                      <Text className="block text-2xl font-bold text-green-600">
                        {analytics.activeInviters}
                      </Text>
                    </View>
                    <View className="bg-purple-50 p-4 rounded-lg">
                      <Text className="text-sm text-gray-500">总奖励积分</Text>
                      <Text className="block text-2xl font-bold text-purple-600">
                        {analytics.totalRewards}
                      </Text>
                    </View>
                  </View>

                  {/* 每日数据统计表格 */}
                  <View>
                    <Text className="font-bold mb-2">每日数据统计</Text>
                    <View className="overflow-x-auto">
                      {/* 桌面端表格 */}
                      <View className="hidden md:block">
                        <View className="grid grid-cols-3 font-bold p-2 bg-gray-100">
                          <Text>日期</Text>
                          <Text>邀请数</Text>
                          <Text>奖励积分</Text>
                        </View>
                        {analytics.dailyStats &&
                        analytics.dailyStats.length > 0 ? (
                          <View>
                            {analytics.dailyStats.map((stat, index) => (
                              <View
                                key={index}
                                className="grid grid-cols-3 p-2 border-b"
                              >
                                <Text>{stat.date}</Text>
                                <Text>{stat.invites}</Text>
                                <Text>{stat.rewards}</Text>
                              </View>
                            ))}
                          </View>
                        ) : (
                          <View className="text-center py-4 text-gray-500">
                            暂无每日统计数据
                          </View>
                        )}
                      </View>

                      {/* 移动端卡片列表 */}
                      <View className="md:hidden space-y-2">
                        {analytics.dailyStats &&
                        analytics.dailyStats.length > 0 ? (
                          analytics.dailyStats.map((stat, index) => (
                            <View
                              key={index}
                              className="bg-gray-50 p-3 rounded"
                            >
                              <Text className="font-bold">{stat.date}</Text>
                              <View className="flex justify-between mt-2">
                                <View>
                                  <Text className="text-xs text-gray-500">
                                    邀请数
                                  </Text>
                                  <Text className="font-medium">
                                    {stat.invites}
                                  </Text>
                                </View>
                                <View>
                                  <Text className="text-xs text-gray-500">
                                    奖励积分
                                  </Text>
                                  <Text className="font-medium">
                                    {stat.rewards}
                                  </Text>
                                </View>
                              </View>
                            </View>
                          ))
                        ) : (
                          <View className="text-center py-4 text-gray-500">
                            暂无每日统计数据
                          </View>
                        )}
                      </View>
                    </View>
                  </View>
                </View>
              )}

              {/* 邀请码列表 */}
              <View className="overflow-x-auto">
                <View className="min-w-full">
                  {/* 桌面端表格视图 */}
                  <View className="hidden md:block">
                    <View className="grid grid-cols-4 font-bold p-2 bg-gray-100">
                      <Text>邀请码</Text>
                      <Text>状态</Text>
                      <Text>创建时间</Text>
                      <Text>使用时间</Text>
                    </View>

                    {invitations.map((invitation) => (
                      <View
                        key={invitation.id}
                        className="grid grid-cols-4 p-2 border-b"
                      >
                        <Text>{invitation.code}</Text>
                        <Text>
                          {invitation.status === "unused" ? "未使用" : "已使用"}
                        </Text>
                        <Text>
                          {new Date(invitation.createTime).toLocaleString()}
                        </Text>
                        <Text>
                          {invitation.usedTime
                            ? new Date(invitation.usedTime).toLocaleString()
                            : "-"}
                        </Text>
                      </View>
                    ))}
                  </View>

                  {/* 移动端卡片视图 */}
                  <View className="md:hidden space-y-4">
                    {invitations.map((invitation) => (
                      <View
                        key={invitation.id}
                        className="bg-white p-4 rounded-lg shadow-sm border"
                      >
                        <View className="space-y-2">
                          <View className="flex justify-between">
                            <Text className="text-gray-500">邀请码:</Text>
                            <Text className="font-medium">
                              {invitation.code}
                            </Text>
                          </View>
                          <View className="flex justify-between">
                            <Text className="text-gray-500">状态:</Text>
                            <Text
                              className={
                                invitation.status === "unused"
                                  ? "text-green-500"
                                  : "text-red-500"
                              }
                            >
                              {invitation.status === "unused"
                                ? "未使用"
                                : "已使用"}
                            </Text>
                          </View>
                          <View className="flex justify-between">
                            <Text className="text-gray-500">创建时间:</Text>
                            <Text>
                              {new Date(invitation.createTime).toLocaleString()}
                            </Text>
                          </View>
                          <View className="flex justify-between">
                            <Text className="text-gray-500">使用时间:</Text>
                            <Text>
                              {invitation.usedTime
                                ? new Date(invitation.usedTime).toLocaleString()
                                : "-"}
                            </Text>
                          </View>
                        </View>
                      </View>
                    ))}
                  </View>
                </View>
              </View>

              {invitations.length === 0 && (
                <View className="text-center py-8 text-gray-500">
                  暂无邀请码数据
                </View>
              )}
            </View>
          )}

          {activeTab === 3 && (
            <View className="space-y-4">
              <View className="flex justify-between mb-4">
                <Text className="text-lg font-bold">奖励配置</Text>
                {rewardConfig && !isEditingReward && (
                  <Button
                    className="text-sm bg-blue-500 text-white px-3 py-1 rounded h-10 w-40"
                    onClick={() => {
                      setEditingRewardForm({
                        name: rewardConfig.name,
                        inviterReward: rewardConfig.inviterReward,
                        inviteeReward: rewardConfig.inviteeReward,
                        taskCreatorReward: rewardConfig.taskCreatorReward,
                        taskParticipantReward:
                          rewardConfig.taskParticipantReward,
                        taskCommissionRate: rewardConfig.taskCommissionRate,
                        description: rewardConfig.description,
                      });
                      setIsEditingReward(true);
                    }}
                  >
                    编辑配置
                  </Button>
                )}
              </View>

              {rewardConfig && !isEditingReward && (
                <View className="bg-white p-4 rounded-lg border">
                  <View className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <View className="border-b pb-2">
                      <Text className="text-gray-500">配置名称</Text>
                      <Text className="block font-bold">
                        {rewardConfig.name}
                      </Text>
                    </View>
                    <View className="border-b pb-2">
                      <Text className="text-gray-500">邀请人奖励</Text>
                      <Text className="block font-bold">
                        {rewardConfig.inviterReward} 积分
                      </Text>
                    </View>
                    <View className="border-b pb-2">
                      <Text className="text-gray-500">被邀请人奖励</Text>
                      <Text className="block font-bold">
                        {rewardConfig.inviteeReward} 积分
                      </Text>
                    </View>
                    <View className="border-b pb-2">
                      <Text className="text-gray-500">任务发布者奖励</Text>
                      <Text className="block font-bold">
                        {rewardConfig.taskCreatorReward} 积分
                      </Text>
                    </View>
                    <View className="border-b pb-2">
                      <Text className="text-gray-500">任务参与者奖励</Text>
                      <Text className="block font-bold">
                        {rewardConfig.taskParticipantReward} 积分
                      </Text>
                    </View>
                    <View className="border-b pb-2">
                      <Text className="text-gray-500">任务分成比例</Text>
                      <Text className="block font-bold">
                        {rewardConfig.taskCommissionRate}%
                      </Text>
                    </View>
                    <View className="col-span-1 md:col-span-2 border-b pb-2">
                      <Text className="text-gray-500">描述</Text>
                      <Text className="block">{rewardConfig.description}</Text>
                    </View>
                    <View className="border-b pb-2">
                      <Text className="text-gray-500">创建时间</Text>
                      <Text className="block">
                        {new Date(rewardConfig.createdAt).toLocaleString()}
                      </Text>
                    </View>
                    <View className="border-b pb-2">
                      <Text className="text-gray-500">更新时间</Text>
                      <Text className="block">
                        {new Date(rewardConfig.updatedAt).toLocaleString()}
                      </Text>
                    </View>
                  </View>
                </View>
              )}

              {isEditingReward && (
                <View className="bg-white p-4 rounded-lg border">
                  <View className="space-y-4">
                    <View>
                      <Text className="block mb-1">配置名称</Text>
                      <Input
                        type="text"
                        value={editingRewardForm.name}
                        onInput={(e) =>
                          setEditingRewardForm({
                            ...editingRewardForm,
                            name: e.detail.value,
                          })
                        }
                        className="border p-2 w-full rounded"
                        placeholder="输入配置名称"
                      />
                    </View>
                    <View>
                      <Text className="block mb-1">邀请人奖励 (积分)</Text>
                      <Input
                        type="number"
                        value={editingRewardForm.inviterReward?.toString()}
                        onInput={(e) =>
                          setEditingRewardForm({
                            ...editingRewardForm,
                            inviterReward: Number(e.detail.value),
                          })
                        }
                        className="border p-2 w-full rounded"
                        placeholder="输入奖励积分数"
                      />
                    </View>
                    <View>
                      <Text className="block mb-1">被邀请人奖励 (积分)</Text>
                      <Input
                        type="number"
                        value={editingRewardForm.inviteeReward?.toString()}
                        onInput={(e) =>
                          setEditingRewardForm({
                            ...editingRewardForm,
                            inviteeReward: Number(e.detail.value),
                          })
                        }
                        className="border p-2 w-full rounded"
                        placeholder="输入奖励积分数"
                      />
                    </View>
                    <View>
                      <Text className="block mb-1">任务发布者奖励 (积分)</Text>
                      <Input
                        type="number"
                        value={editingRewardForm.taskCreatorReward?.toString()}
                        onInput={(e) =>
                          setEditingRewardForm({
                            ...editingRewardForm,
                            taskCreatorReward: Number(e.detail.value),
                          })
                        }
                        className="border p-2 w-full rounded"
                        placeholder="输入奖励积分数"
                      />
                    </View>
                    <View>
                      <Text className="block mb-1">任务参与者奖励 (积分)</Text>
                      <Input
                        type="number"
                        value={editingRewardForm.taskParticipantReward?.toString()}
                        onInput={(e) =>
                          setEditingRewardForm({
                            ...editingRewardForm,
                            taskParticipantReward: Number(e.detail.value),
                          })
                        }
                        className="border p-2 w-full rounded"
                        placeholder="输入奖励积分数"
                      />
                    </View>
                    <View>
                      <Text className="block mb-1">任务分成比例 (%)</Text>
                      <Input
                        type="number"
                        value={editingRewardForm.taskCommissionRate?.toString()}
                        onInput={(e) =>
                          setEditingRewardForm({
                            ...editingRewardForm,
                            taskCommissionRate: Number(e.detail.value),
                          })
                        }
                        className="border p-2 w-full rounded"
                        placeholder="输入分成比例 (1-100)"
                      />
                    </View>
                    <View>
                      <Text className="block mb-1">描述</Text>
                      <Input
                        type="text"
                        value={editingRewardForm.description}
                        onInput={(e) =>
                          setEditingRewardForm({
                            ...editingRewardForm,
                            description: e.detail.value,
                          })
                        }
                        className="border p-2 w-full rounded"
                        placeholder="输入配置描述"
                      />
                    </View>
                    <View className="flex flex-col md:flex-row justify-end pt-4 space-y-2 md:space-y-0 md:space-x-4">
                      <Button
                        className="text-sm bg-gray-500 text-white px-3 py-1 rounded"
                        onClick={() => setIsEditingReward(false)}
                      >
                        取消
                      </Button>
                      <Button
                        className="text-sm bg-blue-500 text-white px-3 py-1 rounded"
                        onClick={handleUpdateRewardConfig}
                      >
                        保存
                      </Button>
                    </View>
                  </View>
                </View>
              )}

              {!rewardConfig && (
                <View className="text-center py-8 text-gray-500">
                  暂无奖励配置数据
                </View>
              )}
            </View>
          )}

          {activeTab === 4 && (
            <View className="space-y-4">
              <View className="flex justify-between mb-4">
                <Text className="text-lg font-bold">用户管理</Text>
                <Text>总计: {userMeta.total} 用户</Text>
              </View>

              {/* 桌面端表格视图 */}
              <View className="hidden md:block overflow-x-auto">
                <View className="min-w-full">
                  <View className="grid grid-cols-6 font-bold p-2 bg-gray-100">
                    <Text>ID</Text>
                    <Text>用户名</Text>
                    <Text>邮箱</Text>
                    <Text>积分</Text>
                    <Text>邀请数</Text>
                    <Text>操作</Text>
                  </View>

                  {allUsers.map((user) => (
                    <View
                      key={user.id}
                      className="grid grid-cols-6 p-2 border-b"
                    >
                      <Text>{user.id}</Text>
                      <Text>{user.username}</Text>
                      <Text>{user.email}</Text>
                      <Text>{user.omn}</Text>
                      <Text>{user.totalInviteCount || 0}</Text>
                      <View className="flex space-x-2">
                        <Text
                          className="text-blue-500 cursor-pointer text-sm"
                          onClick={() => fetchUserDetail(user.id)}
                        >
                          详情
                        </Text>
                        <Text
                          className="text-red-500 cursor-pointer text-sm"
                          onClick={() => handleDeleteUser(user.id)}
                        >
                          删除
                        </Text>
                      </View>
                    </View>
                  ))}
                </View>
              </View>

              {/* 移动端卡片视图 */}
              <View className="md:hidden space-y-4">
                {allUsers.map((user) => (
                  <View
                    key={user.id}
                    className="bg-white p-4 rounded-lg shadow-sm border"
                  >
                    <View className="flex justify-between items-center mb-2">
                      <Text className="font-bold">ID: {user.id}</Text>
                      <View className="flex space-x-2">
                        <Text
                          className="text-blue-500 cursor-pointer text-sm"
                          onClick={() => fetchUserDetail(user.id)}
                        >
                          详情
                        </Text>
                        <Text
                          className="text-red-500 cursor-pointer text-sm"
                          onClick={() => handleDeleteUser(user.id)}
                        >
                          删除
                        </Text>
                      </View>
                    </View>
                    <View className="space-y-1">
                      <View className="flex justify-between">
                        <Text className="text-gray-500">用户名:</Text>
                        <Text>{user.username}</Text>
                      </View>
                      <View className="flex justify-between">
                        <Text className="text-gray-500">邮箱:</Text>
                        <Text>{user.email}</Text>
                      </View>
                      <View className="flex justify-between">
                        <Text className="text-gray-500">积分:</Text>
                        <Text>{user.omn}</Text>
                      </View>
                      <View className="flex justify-between">
                        <Text className="text-gray-500">邀请数:</Text>
                        <Text>{user.totalInviteCount || 0}</Text>
                      </View>
                    </View>
                  </View>
                ))}
              </View>

              {allUsers.length === 0 && (
                <View className="text-center py-8 text-gray-500">
                  暂无用户数据
                </View>
              )}

              <View className="flex justify-between items-center mt-4">
                <Button
                  disabled={userPage <= 1}
                  onClick={() => {
                    const prevPage = Math.max(1, userPage - 1);
                    setUserPage(prevPage);
                    fetchAllUsers(prevPage);
                  }}
                  className={`text-sm px-3 py-1 rounded ${
                    userPage <= 1 ? "bg-gray-300" : "bg-blue-500 text-white"
                  }`}
                >
                  上一页
                </Button>
                <view> </view>
                <Button
                  disabled={userPage >= userMeta.totalPages}
                  onClick={() => {
                    const nextPage = Math.min(
                      userMeta.totalPages,
                      userPage + 1
                    );
                    setUserPage(nextPage);
                    fetchAllUsers(nextPage);
                  }}
                  className={`text-sm px-3 py-1 rounded ${
                    userPage >= userMeta.totalPages
                      ? "bg-gray-300"
                      : "bg-blue-500 text-white"
                  }`}
                >
                  下一页
                </Button>
              </View>
            </View>
          )}

          {activeTab === 5 && (
            <View className="space-y-4">
              <View className="flex justify-between mb-4">
                <Text className="text-lg font-bold">任务管理</Text>
                <Text>总计: {taskMeta.total} 任务</Text>
              </View>

              {/* 桌面端表格视图 */}
              <View className="hidden md:block overflow-x-auto">
                <View className="min-w-full">
                  <View className="grid grid-cols-6 font-bold p-2 bg-gray-100">
                    <Text>ID</Text>
                    <Text>标题</Text>
                    <Text>地址</Text>
                    <Text>状态</Text>
                    <Text>审核情况</Text>
                    <Text>创建者</Text>
                    <Text>操作</Text>
                  </View>

                  {allTasks.map((task) => (
                    <View
                      key={task.id}
                      className="grid grid-cols-6 p-2 border-b"
                    >
                      <Text>{task.id}</Text>
                      <Text>{task.title}</Text>
                      <Text>{task.address}</Text>
                      <Text>{task.status}</Text>
                      <Text>{task.show}</Text>

                      <Text>{task.author}</Text>
                      <View>
                        <Text
                          className="text-blue-500 cursor-pointer text-sm"
                          onClick={() => fetchTaskDetail(task.id)}
                        >
                          详情
                        </Text>
                      </View>
                    </View>
                  ))}
                </View>
              </View>

              {/* 移动端卡片视图 */}
              <View className="md:hidden space-y-4">
                {allTasks.map((task) => (
                  <View
                    key={task.id}
                    className="bg-white p-4 rounded-lg shadow-sm border"
                  >
                    <View className="flex justify-between items-center mb-2">
                      <Text className="font-bold">ID: {task.id}</Text>
                      <Text
                        className="text-blue-500 cursor-pointer text-sm"
                        onClick={() => fetchTaskDetail(task.id)}
                      >
                        详情
                      </Text>
                    </View>
                    <View className="space-y-1">
                      <View className="flex justify-between">
                        <Text className="text-gray-500">标题:</Text>
                        <Text>{task.title}</Text>
                      </View>
                      <View className="flex justify-between">
                        <Text className="text-gray-500">地址:</Text>
                        <Text>{task.address}</Text>
                      </View>
                      <View className="flex justify-between">
                        <Text className="text-gray-500">状态:</Text>
                        <Text>{task.status}</Text>
                      </View>
                      <View className="flex justify-between">
                        <Text className="text-gray-500">审核状态:</Text>
                        <Text>
                          {status(task.show)}
                        </Text>
                      </View>
                      <View className="flex justify-between">
                        <Text className="text-gray-500">创建者:</Text>
                        <Text>{task.author}</Text>
                      </View>
                    </View>
                  </View>
                ))}
              </View>

              {allTasks.length === 0 && (
                <View className="text-center py-8 text-gray-500">
                  暂无任务数据
                </View>
              )}

              <View className="flex justify-between items-center mt-4">
                <Button
                  disabled={taskPage <= 1}
                  onClick={() => {
                    const prevPage = Math.max(1, taskPage - 1);
                    setTaskPage(prevPage);
                    fetchAllTasks(prevPage);
                  }}
                  className={`text-sm px-3 py-1 rounded ${
                    taskPage <= 1 ? "bg-gray-300" : "bg-blue-500 text-white"
                  }`}
                >
                  上一页
                </Button>
                <view> </view>
                <Button
                  disabled={taskPage >= taskMeta.totalPages}
                  onClick={() => {
                    const nextPage = Math.min(
                      taskMeta.totalPages,
                      taskPage + 1
                    );
                    setTaskPage(nextPage);
                    fetchAllTasks(nextPage);
                  }}
                  className={`text-sm px-3 py-1 rounded ${
                    taskPage >= taskMeta.totalPages
                      ? "bg-gray-300"
                      : "bg-blue-500 text-white"
                  }`}
                >
                  下一页
                </Button>
              </View>
            </View>
          )}
        </View>
      </View>

      {/* 用户详情弹窗 */}
      {showUserDetail && selectedUser && (
        <View className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <View className="bg-white p-4 md:p-6 rounded-lg w-[95%] md:w-4/5 max-h-[90vh] md:max-h-[80vh] overflow-y-auto">
            <View className="flex justify-between items-center mb-4">
              <Text className="text-xl font-bold">用户详情</Text>
              <Text
                className="text-gray-500 cursor-pointer text-2xl"
                onClick={() => setShowUserDetail(false)}
              >
                ×
              </Text>
            </View>

            <View className="space-y-4">
              <View className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <View className="border-b pb-2">
                  <Text className="text-gray-500">ID</Text>
                  <Text className="block font-bold">{selectedUser.id}</Text>
                </View>
                <View className="border-b pb-2">
                  <Text className="text-gray-500">用户名</Text>
                  <Text className="block font-bold">
                    {selectedUser.username}
                  </Text>
                </View>
                <View className="border-b pb-2">
                  <Text className="text-gray-500">邮箱</Text>
                  <Text className="block font-bold">{selectedUser.email}</Text>
                </View>
                <View className="border-b pb-2">
                  <Text className="text-gray-500">手机</Text>
                  <Text className="block font-bold">{selectedUser.phone}</Text>
                </View>
                <View className="border-b pb-2">
                  <Text className="text-gray-500">积分</Text>
                  <Text className="block font-bold">{selectedUser.omn}</Text>
                </View>
                <View className="border-b pb-2">
                  <Text className="text-gray-500">注册时间</Text>
                  <Text className="block font-bold">
                    {new Date(selectedUser.createTime).toLocaleString()}
                  </Text>
                </View>
                <View className="border-b pb-2">
                  <Text className="text-gray-500">总邀请数</Text>
                  <Text className="block font-bold">
                    {selectedUser.totalInviteCount}
                  </Text>
                </View>
                <View className="border-b pb-2">
                  <Text className="text-gray-500">今日邀请数</Text>
                  <Text className="block font-bold">
                    {selectedUser.dailyInviteCount}
                  </Text>
                </View>
                <View className="border-b pb-2">
                  <Text className="text-gray-500">审核状态</Text>
                  <Text
                    className={`block font-bold ${
                      selectedUser.approved ? "text-green-500" : "text-red-500"
                    }`}
                  >
                    {selectedUser.approved ? "已通过" : "未通过"}
                  </Text>
                </View>
              </View>

              <View className="flex flex-col md:flex-row justify-end mt-4 space-y-2 md:space-y-0 md:space-x-4">
                <Button
                  className="text-sm bg-red-500 text-white px-3 py-1 rounded"
                  onClick={() => handleDeleteUser(selectedUser.id)}
                >
                  删除用户
                </Button>
                <Button
                  className="text-sm bg-gray-500 text-white px-3 py-1 rounded"
                  onClick={() => setShowUserDetail(false)}
                >
                  关闭
                </Button>
              </View>
            </View>
          </View>
        </View>
      )}

      {/* 任务详情弹窗 */}
      {showTaskDetail && selectedTask && (
        <View className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <View className="bg-white p-4 md:p-6 rounded-lg w-[95%] md:w-4/5 max-h-[90vh] md:max-h-[80vh] overflow-y-auto">
            <View className="flex justify-between items-center mb-4">
              <Text className="text-xl font-bold">任务详情</Text>
              <Text
                className="text-gray-500 cursor-pointer text-2xl"
                onClick={() => setShowTaskDetail(false)}
              >
                ×
              </Text>
            </View>

            <View className="space-y-4">
              <View className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <View className="border-b pb-2">
                  <Text className="text-gray-500">ID</Text>
                  <Text className="block font-bold">{selectedTask.id}</Text>
                </View>
                <View className="border-b pb-2">
                  <Text className="text-gray-500">标题</Text>
                  <Text className="block font-bold">{selectedTask.title}</Text>
                </View>
                <View className="border-b pb-2">
                  <Text className="text-gray-500">地址</Text>
                  <Text className="block font-bold">
                    {selectedTask.address}
                  </Text>
                </View>
                <View className="border-b pb-2">
                  <Text className="text-gray-500">联系方式</Text>
                  <Text className="block font-bold">{selectedTask.phone}</Text>
                </View>
                <View className="border-b pb-2">
                  <Text className="text-gray-500">状态</Text>
                  <Text className="block font-bold">{selectedTask.status}</Text>
                </View>
                <View className="border-b pb-2">
                  <Text className="text-gray-500">创建者ID</Text>
                  <Text className="block font-bold">{selectedTask.author}</Text>
                </View>
                <View className="border-b pb-2">
                  <Text className="text-gray-500">开始时间</Text>
                  <Text className="block font-bold">
                    {selectedTask.startTime}
                  </Text>
                </View>
                <View className="border-b pb-2">
                  <Text className="text-gray-500">结束时间</Text>
                  <Text className="block font-bold">
                    {selectedTask.endTime}
                  </Text>
                </View>
              </View>

              <View className="mt-4">
                <Text className="text-gray-500 mb-2">任务内容:</Text>
                <View className="bg-gray-100 p-4 rounded">
                  <Text>{selectedTask.content}</Text>
                </View>
              </View>

              <View className="mt-4">
                <Text className="text-gray-500 mb-2">
                  参与用户 ({selectedTask.users?.length || 0}):
                </Text>
                {selectedTask.users && selectedTask.users.length > 0 ? (
                  <View className="bg-gray-100 p-2 rounded">
                    {/* 桌面端表格视图 */}
                    <View className="hidden md:block">
                      <View className="grid grid-cols-4 font-bold mb-2">
                        <Text>ID</Text>
                        <Text>用户名</Text>
                        <Text>邮箱</Text>
                        <Text>操作</Text>
                      </View>
                      {selectedTask.users.map((user) => (
                        <View
                          key={user.id}
                          className="grid grid-cols-4 py-2 border-t"
                        >
                          <Text>{user.id}</Text>
                          <Text>{user.username}</Text>
                          <Text>{user.email}</Text>
                          <Text
                            className="text-blue-500 cursor-pointer text-sm"
                            onClick={() => {
                              setShowTaskDetail(false);
                              fetchUserDetail(user.id);
                            }}
                          >
                            查看用户
                          </Text>
                        </View>
                      ))}
                    </View>

                    {/* 移动端卡片视图 */}
                    <View className="md:hidden space-y-2">
                      {selectedTask.users.map((user) => (
                        <View key={user.id} className="p-2 border-t">
                          <View className="flex justify-between mb-1">
                            <Text className="font-bold">ID: {user.id}</Text>
                            <Text
                              className="text-blue-500 cursor-pointer text-sm"
                              onClick={() => {
                                setShowTaskDetail(false);
                                fetchUserDetail(user.id);
                              }}
                            >
                              查看用户
                            </Text>
                          </View>
                          <View className="space-y-1">
                            <View>
                              <Text className="text-gray-500">用户名: </Text>
                              <Text>{user.username}</Text>
                            </View>
                            <View>
                              <Text className="text-gray-500">邮箱: </Text>
                              <Text>{user.email}</Text>
                            </View>
                          </View>
                        </View>
                      ))}
                    </View>
                  </View>
                ) : (
                  <View className="text-center py-4 text-gray-500 bg-gray-100 rounded">
                    暂无参与用户
                  </View>
                )}
              </View>

              <View className="flex justify-end mt-4">
                <Button
                  className="text-sm bg-gray-500 text-white px-3 py-1 rounded"
                  onClick={() => setShowTaskDetail(false)}
                >
                  关闭
                </Button>
              </View>
            </View>
          </View>
        </View>
      )}
    </View>
  );
}
