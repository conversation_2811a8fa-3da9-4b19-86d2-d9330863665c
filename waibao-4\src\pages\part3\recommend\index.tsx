import { ScrollView, View, Image } from "@tarojs/components";
import { AtSearchBar } from "taro-ui";
import { useEffect, useState } from "react";
import icon1 from "@/images/icon-1.jpg";
import icon2 from "@/images/icon-2.jpg";
import icon3 from "@/images/icon-3.jpg";
import Card from "@/pages/part2/dashboard/card";
import useGetLanguage from "@/pages/hook/useGetLanguage";
import Taro from "@tarojs/taro";
import "./index.scss";
import useLogin from "@/pages/hook/useLogin";
import { getTime } from "@/pages/utils";
import { BackButton } from '@/components/BackButton'

interface CardData {
  id: number;
  title: string;
  address: string;
  startTime: string;
  endTime: string;
  time?: string;
  phone: string;
  status: string;
}

const ENV = Taro.getEnv();
let data:any = [];
export default function Index() {
  let language = useGetLanguage("Home");
  let [cardDatas, setCardDatas] = useState<CardData[]>([]);
  let [value, setValue] = useState("");
  let [showMenu, setShowMenu] = useState(false);
  let token = useLogin();
  let omn = Taro.getStorageSync("omn");

  const handleLogout = () => {
    Taro.clearStorageSync();
    Taro.redirectTo({ url: "/pages/part1/login/index" });
  };

  const handleSwitchRole = () => {
    Taro.redirectTo({ url: "/pages/part1/select/index" });
  };

  const fetchData = () => {
    const token = Taro.getStorageSync("token");
    if (!token) {
      Taro.redirectTo({ url: "/pages/part1/login/index" });
      return;
    }

    Taro.request({
      url: `${process.env.TARO_APP_HOST}/api/broadcastroom`,
      method: "GET",
      header: {
        "Content-Type": "application/json",
        token,
      },
    }).then((res) => {
      console.log('获取数据响应:', res);
      if (res.statusCode === 200 && res.data.success) {
        const data = res.data.data || [];
        const processedData = data.map((item: CardData) => ({
          ...item,
          time: getTime({ endTime: item.endTime, startTime: item.startTime }).day,
        }));
        setCardDatas(processedData.reverse());
      } else {
        console.error('获取数据失败:', res);
        Taro.showToast({
          title: '获取数据失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      console.error('请求失败:', err);
      if (err.statusCode === 401) {
        Taro.redirectTo({ url: "/pages/part1/login/index" });
      }
    });
  };

  const searchData = (keyword: string) => {
    const token = Taro.getStorageSync("token");
    if (!token) {
      Taro.redirectTo({ url: "/pages/part1/login/index" });
      return;
    }

    Taro.request({
      url: `${process.env.TARO_APP_HOST}/api/broadcastroom/search/approved`,
      method: "GET",
      data: {
        keyword
      },
      header: {
        "Content-Type": "application/json",
        token,
      },
    }).then((res) => {
      console.log('搜索数据响应:', res);
      if (res.statusCode === 200 && res.data.success) {
        const data = res.data.data?.data || [];
        const processedData = data.map((item: CardData) => ({
          ...item,
          time: getTime({ endTime: item.endTime, startTime: item.startTime }).day,
        }));
        setCardDatas(processedData.reverse());
      } else {
        console.error('搜索数据失败:', res);
        Taro.showToast({
          title: '搜索失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      console.error('搜索请求失败:', err);
      if (err.statusCode === 401) {
        Taro.redirectTo({ url: "/pages/part1/login/index" });
      }
    });
  };

  useEffect(() => {
    fetchData();
  }, []);

  const onChange = (e) => {
    setValue(e);
  };

  const onActionClick = () => {
    if (value === '') {
      fetchData(); // 重新获取所有数据
    } else {
      searchData(value); // 调用后端搜索接口
    }
  };

  const handleUserCenter = () => {
    Taro.navigateTo({ url: "/pages/user/center/index" });
  };


  return (
    <View className={ENV === "WEB" ? "pt-2 pb-30 page-gradient" : "pt-12 pb-30 page-gradient"}>
      <BackButton />
      <View 
        className="fixed right-4 bottom-4 z-50 w-10 h-10 bg-[#1659c0] rounded-full flex items-center justify-center shadow-lg"
        onClick={() => setShowMenu(!showMenu)}
      >
        <View className="i-ic-baseline-menu text-white text-2xl" />
      </View>

      {showMenu && (
        <View className="fixed right-4 bottom-16 z-50 bg-white rounded-lg shadow-lg overflow-hidden">
          <View 
            className="px-4 py-2 flex items-center hover:bg-gray-100 active:bg-gray-200"
            onClick={handleUserCenter}
          >
            <View className="i-ic-baseline-person mr-2 text-[#1659c0]" />
            <View className="text-sm text-gray-700">{language["User center"]}</View>
          </View>
          <View 
            className="px-4 py-2 flex items-center hover:bg-gray-100 active:bg-gray-200"
            onClick={handleSwitchRole}
          >
            <View className="i-ic-baseline-switch-account mr-2 text-[#1659c0]" />
            <View className="text-sm text-gray-700">{language["Switch role"]}</View>
          </View>
          <View 
            className="px-4 py-2 flex items-center hover:bg-gray-100 active:bg-gray-200 border-t border-gray-100"
            onClick={handleLogout}
          >
            <View className="i-ic-baseline-logout mr-2 text-[#1659c0]" />
            <View className="text-sm text-gray-700">{language["Logout"]}</View>
          </View>
        </View>
      )}

      <AtSearchBar
        actionName="搜索"
        placeholder={language["Enter the keyword"]}
        value={value}
        className="ml-5"
        onClear={() => {
          setValue("")
          fetchData()
        }}
        customStyle={ENV == "WEB" ? "width: 90%" : "width: 70%"}
        // focus
        onChange={onChange}
        onActionClick={onActionClick}
      />
      <View className="p-3 mx-5 rounded-xl bg-gradient-to-r2 mt-3 flex justify-between items-center">
        <View>
          <View className=" text-xs text-gray-600">
            {language["Activity integral"]}
          </View>
          <View className=" text-lg font-bold text-[#1659c0]">
            {omn}
            <View className="i-ic-outline-arrow-right text-[#1659c0] inline-block align-middle "></View>
          </View>
        </View>
        <View className=" rounded-lg bg-[#ffffff9c] text-[#1659c0] text-xs px-3 py-2 font-bold">
          {language["Total tasks"]}：{cardDatas.length}
        </View>
      </View>
      <View className="p-3 mx-5 rounded-xl mt-3 flex justify-center items-center bg-gradient-to-r3 h-200">
        <View className="text-lg font-bold text-[#1659c0] w-[30%] text-left">
          <View className="text-xs text-[#1659c07a]">TASKDESK</View>
          <View className="text-sm text-[#1659c0] font-bold whitespace-nowrap">
            {language["Task Center"]}
          </View>
          <View
            className=" text-center py-1.5 inline-block px-1.5 bg-[#1659c0] text-xs text-white rounded-lg"
            onClick={() => {
              Taro.navigateTo({
                url: "/pages/part3/list/index",
              });
            }}
          >
            {language["Go"]}
            <View className="i-ic-baseline-arrow-circle-right text-md inline-block align-middle"></View>
          </View>
        </View>
        <View className="w-[70%] ">
          <ScrollView
            className="scroll-view  bg-white p-2.5 box-border rounded-xl"
            scrollX
            scrollLeft={1}
            enableFlex
            scrollWithAnimation
          >
            <View className="scroll-item p-2 rounded-xl relative bg-gradient-to-r4 ">
              <View className=" text-center absolute -top-2 left-[40%]">
                <Image
                  mode="widthFix"
                  className="rounded-lg w-50 icon-deg45"
                  src={icon1}
                ></Image>
              </View>
              <View className=" font-bold text-sm text-center mt-5">
                1.{language["login"]}
              </View>
              <View className=" text-xs text-gray-500 text-center">
                {language["join us"]}
              </View>
            </View>
            <View className="scroll-item p-2 rounded-xl relative  bg-gradient-to-r5 ">
              <View className=" text-center absolute -top-2 left-[40%]">
                <Image
                  mode="widthFix"
                  className="rounded-lg w-50  icon-deg45"
                  src={icon2}
                ></Image>
              </View>
              <View className=" font-bold text-sm text-center mt-5">
                2.{language["Select task"]}
              </View>
              <View className=" text-xs text-gray-500 text-center">
                {language["Participating task"]}
              </View>
            </View>
            <View className="scroll-item p-2 rounded-xl relative  bg-gradient-to-r6 ">
              <View className=" text-center absolute -top-2 left-[40%]">
                <Image
                  mode="widthFix"
                  className="rounded-lg w-50 icon-deg45"
                  src={icon3}
                ></Image>
              </View>
              <View className=" font-bold text-sm text-center mt-5">
                3. {language["Fulfil"]}
              </View>
              <View className=" text-xs text-gray-500 text-center">
                {language["earn point"]}
              </View>
            </View>
          </ScrollView>
        </View>
      </View>
      <View>
        <View className="font-bold text-lg py-3 mx-5">
          {language["Hot task"]}
        </View>
        <View>
          {cardDatas && cardDatas.length > 0 ? (
            cardDatas.map((item: CardData) => (
              <Card key={item.id} item={item} isRecommend={true} />
            ))
          ) : (
            <View className="text-center text-gray-500 py-8">
              暂无任务
            </View>
          )}
        </View>
      </View>
    </View>
  );
}
