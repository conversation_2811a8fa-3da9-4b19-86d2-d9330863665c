import { MiddlewareConsumer, <PERSON><PERSON>le, RequestMethod } from '@nestjs/common';
import { UserService } from './user.service';
import { UserController } from './user.controller';
import { User } from './entities/user.entity';
import { TypeOrmModule } from '@nestjs/typeorm';
import { InviteModule } from '../invite/invite.module';
import { EmailModule } from '../email/email.module';

@Module({
  controllers: [UserController],
  imports: [
    TypeOrmModule.forFeature([User]),
    InviteModule,
    EmailModule
  ],
  providers: [UserService],
  exports: [UserService],
})
export class UserModule {}
