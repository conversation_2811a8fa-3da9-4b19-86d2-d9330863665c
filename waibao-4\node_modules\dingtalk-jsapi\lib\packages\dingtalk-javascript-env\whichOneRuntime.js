"use strict";function snifferMachine(e,n){for(var i=e.length,a=0,f=!0;a<i;a++)try{if(!(e[a]in n)){f=!1;break}}catch(e){f=!1;break}return f}function whichOneRuntime(){return maybeInWebView&&maybeInWeexVueEnv?snifferMachine(snifferWeexVueMap,weex)?"Web.Vue":"Web.Unknown":!maybeInWebView&&maybeInWeexVueEnv?snifferMachine(snifferWeexVueMap,weex)?"Weex.Vue":"Weex.Unknown":maybeInWebView&&maybeInNative&&!maybeInWeexVueEnv?snifferMachine(snifferWeexRaxMap,window)?"Weex.Rax":"Weex.Unknown":maybeInWebView&&snifferMachine(snifferWebViewMap,window)?"Web.Unknown":"Unknown.Unknown"}Object.defineProperty(exports,"__esModule",{value:!0});var maybeInWebView="undefined"!=typeof window,maybeInWeexVueEnv="undefined"!=typeof weex,maybeInNative="undefined"!=typeof callNative,snifferWeexRaxMap=["__weex_config__","__weex_options__","__weex_require__"],snifferWebViewMap=["localStorage","location","navigator","XMLHttpRequest"],snifferWeexVueMap=["config","requireModule","document"];exports.default=whichOneRuntime;