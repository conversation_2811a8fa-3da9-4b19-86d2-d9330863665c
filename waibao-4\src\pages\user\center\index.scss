@import "~taro-ui/dist/style/components/icon.scss";

.at-icon-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.user-center {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding: 1rem;
  box-sizing: border-box;
}

.card {
  background-color: #ffffff;
  border-radius: 0.5rem;
  padding: 1rem;
  margin-bottom: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.copy-button {
  background-color: #1659c0;
  color: white;
  padding: 0.25rem 1rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  cursor: pointer;
  
  &:active {
    background-color: darken(#1659c0, 10%);
  }
}

.stats-box {
  background-color: #f8faff;
  border: 1px solid #e6efff;
  border-radius: 0.5rem;
  padding: 1rem;
  
  .label {
    color: #666;
    font-size: 0.875rem;
  }
  
  .value {
    color: #1659c0;
    font-size: 1.25rem;
    font-weight: bold;
    margin-top: 0.25rem;
  }
} 