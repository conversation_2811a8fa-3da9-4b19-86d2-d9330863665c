import {
  Injectable,
  NestInterceptor,
  Execution<PERSON>ontext,
  Call<PERSON><PERSON>ler,
  HttpStatus,
} from '@nestjs/common';
import { Observable, throwError } from 'rxjs';
import { map, catchError, tap, takeWhile } from 'rxjs/operators';

interface Response<T> {
  data: T;
}

@Injectable()
export class TransformInterceptor<T>
  implements NestInterceptor<T, Response<T>>
{
  intercept(
    context: ExecutionContext,
    next: CallHandler,
  ): Observable<Response<T>> {
    return next.handle().pipe(
      map(data => {
        this.logResponse(data);
        
        if (data && typeof data === 'object' && 'code' in data && 'data' in data && 'success' in data) {
          return data;
        }
        
        return {
          code: context.switchToHttp().getResponse().statusCode || 200,
          data,
          message: '操作成功',
          success: true
        };
      }),
    );
  }
  
  private logResponse(data: any) {
    console.log('响应数据结构:', JSON.stringify({
      isObject: typeof data === 'object',
      hasCode: data && typeof data === 'object' && 'code' in data,
      hasData: data && typeof data === 'object' && 'data' in data,
      hasSuccess: data && typeof data === 'object' && 'success' in data,
      keys: data && typeof data === 'object' ? Object.keys(data) : []
    }));
  }
}
