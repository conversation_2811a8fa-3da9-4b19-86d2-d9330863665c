"use strict";function padNumber(e){return e="00"+e,e.substring(e.length-2,e.length)}var __spreadArray=this&&this.__spreadArray||function(e,t,r){if(r||2===arguments.length)for(var o,a=0,n=t.length;a<n;a++)!o&&a in t||(o||(o=Array.prototype.slice.call(t,0,a)),o[a]=t[a]);return e.concat(o||Array.prototype.slice.call(t))};Object.defineProperty(exports,"__esModule",{value:!0}),exports.log=void 0;var log=function(e){console.log.apply(console,__spreadArray(["".concat(padNumber(e.time.getHours()),":").concat(padNumber(e.time.getMinutes()),":").concat(padNumber(e.time.getSeconds()))],e.text,!1))};exports.log=log;