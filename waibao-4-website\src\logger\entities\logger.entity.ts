import {
  Column,
  CreateDateColumn,
  Entity,
  PrimaryColumn,
  PrimaryGeneratedColumn,
} from 'typeorm';

@Entity('logs_table')
export class Logger {
  @PrimaryGeneratedColumn({
    comment: 'id',
  })
  id: number;
  @Column({
    comment: '请求方法',
  })
  method: string;
  @Column({
    comment: '请求地址',
  })
  url: string;
  @Column({
    comment: '请求参数',
  })
  param: string;
  @Column({
    comment: '请求用户',
  })
  user: string;
  @Column({
    comment: '使用权限',
  })
  permission: string;
  @CreateDateColumn({
    comment: '创建时间',
    type: 'timestamp',
  })
  createTime: Date;
}
