import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsString, Length, Matches } from 'class-validator';

export class RequestResetDto {
  @ApiProperty({ 
    description: '邮箱',
    example: '<EMAIL>'
  })
  @IsEmail({}, { message: '请输入有效的邮箱地址' })
  email: string;
}

export class ResetPasswordDto {
  @ApiProperty({ 
    description: '邮箱',
    example: '<EMAIL>'
  })
  @IsEmail({}, { message: '请输入有效的邮箱地址' })
  email: string;

  @ApiProperty({ 
    description: '验证码',
    example: '123456'
  })
  @IsString()
  @Length(6, 6, { message: '验证码必须是6位' })
  verificationCode: string;

  @ApiProperty({ 
    description: '新密码',
    example: 'newPassword123'
  })
  @IsString()
  @Length(6, 20, { message: '密码长度必须在6-20位之间' })
  @Matches(
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]{6,20}$/,
    { message: '密码必须包含大小写字母和数字' }
  )
  newPassword: string;
} 