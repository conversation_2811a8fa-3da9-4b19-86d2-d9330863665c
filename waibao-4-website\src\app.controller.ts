import {
  <PERSON>,
  Get,
  Header,
  HttpException,
  HttpStatus,
  Ip,
  Render,
  Req,
  Res,
  Session,
  UseFilters,
  UseGuards,
} from '@nestjs/common';
import { AppService } from './app.service';
import { appFilter } from './app.filter';
import { ConfigService } from '@nestjs/config';
import { ApiTags } from '@nestjs/swagger';
import { join } from 'path';
import { LoginGuard } from './login.guard';
import { Request, Response } from 'express';

@Controller()
@ApiTags('app路由信息')
export class AppController {
  constructor(
    private readonly appService: AppService,
    private readonly configService: ConfigService,
  ) {}

  @Get()
  async index() {
    return this.appService.getHello();
  }

}
