import { View, Image } from "@tarojs/components";
import { AtAvatar } from "taro-ui";
import "./index.scss";

const Product = () => (
  <View className="w-[49%] rounded-lg bg-white overflow-hidden mt-1.5">
    <Image
      src="https://cdn.shopifycdn.net/s/files/1/0269/0652/5774/products/culturelle-probiotic-prebiotic-gummies-front.png?crop=center&height=600&v=1681738946&width=600"
      className="w-full h-[350px]"
    />
    <View className=" text-black p-1.5 font-bold text-sm">
      Culturelle® Metabolism + Weight Management Capsules
    </View>
    <View className=" text-red-6 px-1.5 font-bold">￥198.00</View>
    <View className=" flex justify-between items-center p-1.5">
      <View className=" text-gray-400 text-sm">已售8955</View>
      <View className=" bg-red-600 text-white rounded-2xl w-[50px] h-[50px] flex justify-center items-center">
        +
      </View>
    </View>
  </View>
);

export default function User() {
  return (
    <View
      style={{
        background: `url("https://7465-test-7gnmgwm92a9bbdc4-1312662256.tcb.qcloud.la/1.jpg?sign=97a85b510a11cff81b14c5d2b40d7736&t=1697781828") repeat`,
      }}
    >
      <View className="flex justify-between items-center text-sm text-gray-400 px-3.5 py-5">
        <View className="flex justify-start items-center">
          <AtAvatar
            circle
            className="border-2 border-indigo-600 mx-2.5"
            image="https://7465-test-7gnmgwm92a9bbdc4-1312662256.tcb.qcloud.la/banner.jpg"
          ></AtAvatar>
          <View>
            <View className=" font-bold text-lg text-black">测试昵称</View>
            <View>普通会员</View>
          </View>
        </View>
        <View className=" text-black">账号ID：10086</View>
      </View>
      <View className="text-sm px-3.5 py-2.5 mx-1.5 my-2 rounded-lg">
        <View className="flex justify-between items-center">
          <View className="text-center w-1/3">
            <View className=" font-bold text-lg">183900</View>
            <View className=" text-gray-400">积分</View>
          </View>
          <View className="text-center w-1/3">
            <View className=" font-bold text-lg">183900</View>
            <View className=" text-gray-400">即将过期</View>
          </View>
          <View className="text-center w-1/3">
            <View className=" font-bold text-lg text-red-600">点击兑换</View>
            <View className=" text-gray-400">筋膜枪</View>
          </View>
        </View>
      </View>
      <View className=" flex justify-center items-center pb-1.5 mx-1.5 my-2 rounded-lg">
        <Image
          className="w-full h-[100px] "
          src="https://7465-test-7gnmgwm92a9bbdc4-1312662256.tcb.qcloud.la/usericon.png?sign=674ae4ed53e55cdb282378dabaeb9acc&t=1697782418"
        />
      </View>
      <View className="bg-white text-sm px-3.5 py-5 mx-1.5 my-2 rounded-lg">
        <View className=" flex justify-between item-center mb-5">
          <View className=" font-bold text-lg">我的订单</View>
          <View className=" text-gray-500">全部订单</View>
        </View>
        <View className="flex justify-between items-center">
          <View className="text-center w-1/3">
            <View className=" text-lg i-iconamoon-credit-card w-[60px] h-[60px] inline-block"></View>
            <View className=" text-gray-400">待发货</View>
          </View>
          <View className="text-center w-1/3">
            <View className=" text-lg i-iconamoon-delivery-fast-light w-[60px] h-[60px] inline-block"></View>
            <View className=" text-gray-400">待收货</View>
          </View>
          <View className="text-center w-1/3">
            <View className=" text-lg i-iconamoon-discover w-[60px] h-[60px] inline-block"></View>
            <View className=" text-gray-400">地址管理</View>
          </View>
        </View>
      </View>
      <View className="bg-white text-sm px-3.5 py-5 mx-1.5 my-2 rounded-lg">
        <View className=" font-bold text-lg text-left mb-5">我的资产</View>
        <View className="flex justify-between items-center">
          <View className="text-center w-1/3">
            <View className=" font-bold text-lg">1200</View>
            <View className=" text-gray-400">积分</View>
          </View>
          <View className=" w-[1px] h-[80px] bg-gray-200"></View>
          <View className="text-center w-1/3">
            <View className=" font-bold text-lg">899.99</View>
            <View className=" text-gray-400">余额</View>
          </View>
          <View className=" w-[1px] h-[80px] bg-gray-200"></View>
          <View className="text-center w-1/3">
            <View className=" font-bold text-lg text-red-600">5</View>
            <View className=" text-gray-400">优惠券</View>
          </View>
        </View>
      </View>
      <View className="bg-white text-sm px-3.5 py-5 mx-1.5 my-2 rounded-lg">
        <View className=" font-bold text-lg text-left mb-5">服务与工具</View>
        <View className="flex justify-between items-center">
          <View className="text-center w-1/3">
            <View className=" font-bold text-lg">1200</View>
            <View className=" text-gray-400">积分</View>
          </View>
          <View className=" w-[1px] h-[80px] bg-gray-200"></View>
          <View className="text-center w-1/3">
            <View className=" font-bold text-lg">899.99</View>
            <View className=" text-gray-400">余额</View>
          </View>
          <View className=" w-[1px] h-[80px] bg-gray-200"></View>
          <View className="text-center w-1/3">
            <View className=" font-bold text-lg text-red-600">5</View>
            <View className=" text-gray-400">优惠券</View>
          </View>
        </View>
      </View>
      <View>
        <View className=" text-gray-300 flex justify-center items-center my-4.5">
          ——
          <View className=" font-bold text-black mx-1.5">好物推荐</View>
          ——
        </View>
        <View className="px-1.5 flex justify-between items-start flex-wrap">
          {new Array(50).fill(0).map(() => (
            <Product />
          ))}
        </View>
      </View>
    </View>
  );
}
