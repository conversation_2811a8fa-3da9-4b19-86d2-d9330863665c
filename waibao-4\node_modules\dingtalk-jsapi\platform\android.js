"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var ddSdk_1=require("../lib/ddSdk"),env_1=require("../lib/env"),sdk_1=require("../lib/sdk"),eapp_1=require("../lib/bridge/eapp"),webviewInMiniApp_1=require("../lib/bridge/webviewInMiniApp"),h5Android_1=require("../lib/bridge/h5Android"),weex_1=require("../lib/bridge/weex"),h5Event_1=require("../lib/bridge/h5Event"),weexEvent_1=require("../lib/bridge/weexEvent");ddSdk_1.ddSdk.setPlatform({platform:env_1.ENV_ENUM.android,bridgeInit:function(){var e=(0,env_1.getENV)();return e.appType===sdk_1.APP_TYPE.MINI_APP?Promise.resolve(eapp_1.default):e.appType===sdk_1.APP_TYPE.WEBVIEW_IN_MINIAPP?Promise.resolve(webviewInMiniApp_1.default):e.appType===sdk_1.APP_TYPE.WEEX?(0,weex_1.androidWeexBridge)():(0,h5Android_1.h5AndroidbridgeInit)().then(function(){return h5Android_1.default})},authMethod:"runtime.permission.requestJsApis",event:{on:function(e,r){var i=(0,env_1.getENV)();switch(i.appType){case sdk_1.APP_TYPE.WEB:case sdk_1.APP_TYPE.WEBVIEW_IN_MINIAPP:(0,h5Event_1.on)(e,r);break;case sdk_1.APP_TYPE.WEEX:(0,weexEvent_1.on)(e,r);break;default:throw new Error("Not support global event in the platfrom: ".concat(i.appType))}},off:function(e,r){var i=(0,env_1.getENV)();switch(i.appType){case sdk_1.APP_TYPE.WEB:case sdk_1.APP_TYPE.WEBVIEW_IN_MINIAPP:(0,h5Event_1.off)(e,r);break;case sdk_1.APP_TYPE.WEEX:(0,weexEvent_1.off)(e,r);break;default:throw new Error("Not support global event in the platfrom: ".concat(i.appType))}}}});