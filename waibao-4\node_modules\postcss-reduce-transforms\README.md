# [postcss][postcss]-reduce-transforms

> Reduce transform functions with PostCSS.

## Install

With [npm](https://npmjs.org/package/postcss-reduce-transforms) do:

```
npm install postcss-reduce-transforms --save
```

## Example

This module will reduce transform functions where possible. For more examples,
see the [tests](src/__tests__/index.js).

### Input

```css
h1 {
  transform: rotate3d(0, 0, 1, 20deg);
}
```

### Output

```css
h1 {
  transform: rotate(20deg);
}
```

## Usage

See the [PostCSS documentation](https://github.com/postcss/postcss#usage) for
examples for your environment.

## Contributors

See [CONTRIBUTORS.md](https://github.com/cssnano/cssnano/blob/master/CONTRIBUTORS.md).

## License

MIT © [<PERSON>](http://beneb.info)

[postcss]: https://github.com/postcss/postcss
