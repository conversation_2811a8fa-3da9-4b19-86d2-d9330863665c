import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, ManyToOne } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { User } from '../../user/entities/user.entity';

@Entity('invite_records')
export class InviteRecord {
  @ApiProperty({ description: '记录ID' })
  @PrimaryGeneratedColumn()
  id: number;

  @ApiProperty({ description: '邀请人ID' })
  @Column()
  inviterId: number;

  @ApiProperty({ description: '被邀请人ID' })
  @Column()
  inviteeId: number;

  @ApiProperty({ 
    description: '奖励积分',
    example: 10
  })
  @Column({
    comment: '奖励积分',
  })
  points: number;

  @ApiProperty({ 
    description: '奖励类型',
    enum: ['REGISTER', 'REGISTER_BONUS', 'TASK_PARTICIPATE', 'TASK_CREATE', 'TASK_COMMISSION'],
    example: 'REGISTER'
  })
  @Column({
    comment: '奖励类型',
    type: 'enum',
    enum: ['REGISTER', 'REGISTER_BONUS', 'TASK_PARTICIPATE', 'TASK_CREATE', 'TASK_COMMISSION']
  })
  rewardType: string;

  @ApiProperty({ description: '创建时间' })
  @CreateDateColumn()
  createdAt: Date;

  @ApiProperty({ description: '邀请人信息' })
  @ManyToOne(() => User)
  inviter: User;

  @ApiProperty({ description: '被邀请人信息' })
  @ManyToOne(() => User)
  invitee: User;
} 