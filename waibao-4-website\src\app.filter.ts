import {
  ArgumentsHost,
  Catch,
  ExceptionFilter,
  HttpException,
} from '@nestjs/common';
import { Response } from 'express';
@Catch(HttpException)
export class appFilter implements ExceptionFilter {
  catch(exception: HttpException, host: ArgumentsHost) {
      const response: Response = host.switchToHttp().getResponse();
    response.status(exception.getStatus()).json({
        msg: exception.message,
        error: exception.cause.message
    });
  }
}
