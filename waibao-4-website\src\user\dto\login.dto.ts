import { IsEmail, IsString, IsOptional, Length } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class LoginDto {
  @ApiProperty({ 
    description: '邮箱',
    example: '<EMAIL>'
  })
  @IsEmail({}, { message: '请输入有效的邮箱地址' })
  email: string;

  @ApiProperty({ 
    description: '密码',
    example: '123456'
  })
  @IsString()
  @Length(6, 20, { message: '密码长度必须在6-20位之间' })
  password: string;

  @ApiProperty({ 
    description: '微信OpenID',
    required: false
  })
  @IsString()
  @IsOptional()
  wxOpenid?: string;
}
