"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.uniEventOff=exports.uniEventOn=void 0;var env_1=require("../env"),h5Event_1=require("./h5Event"),weexEvent_1=require("./weexEvent"),h5PcEvent_1=require("./h5PcEvent"),combineBridge_1=require("./combineBridge"),env=(0,env_1.getENV)(),NOT_SUPPORT_MSG="Not support uni event in the platfrom: ".concat(env.platform,", appType: ").concat(env.appType),uniEventOn=function(e,n){(0,combineBridge_1.getUniBridge)().then(function(){switch(env.platform){case env_1.ENV_ENUM.ios:case env_1.ENV_ENUM.android:case env_1.ENV_ENUM.harmony:switch(env.appType){case env_1.APP_TYPE.WEB:return(0,h5Event_1.on)(e,n);case env_1.APP_TYPE.WEEX:return(0,weexEvent_1.on)(e,n);default:throw new Error(NOT_SUPPORT_MSG)}case env_1.ENV_ENUM.pc:if(env.appType===env_1.APP_TYPE.WEB)return(0,h5PcEvent_1.on)(e,n);throw new Error(NOT_SUPPORT_MSG);default:throw new Error(NOT_SUPPORT_MSG)}})};exports.uniEventOn=uniEventOn;var uniEventOff=function(e,n){(0,combineBridge_1.getUniBridge)().then(function(){switch(env.platform){case env_1.ENV_ENUM.ios:case env_1.ENV_ENUM.android:switch(env.appType){case env_1.APP_TYPE.WEB:return(0,h5Event_1.off)(e,n);case env_1.APP_TYPE.WEEX:return(0,weexEvent_1.off)(e,n);default:throw new Error(NOT_SUPPORT_MSG)}case env_1.ENV_ENUM.pc:if(env.appType===env_1.APP_TYPE.WEB)return(0,h5PcEvent_1.off)(e,n);throw new Error(NOT_SUPPORT_MSG);default:throw new Error(NOT_SUPPORT_MSG)}})};exports.uniEventOff=uniEventOff;