import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import * as session from 'express-session';
import { NestExpressApplication } from '@nestjs/platform-express';
import * as cookieParser from 'cookie-parser';
import { join } from 'path';
import * as fs from 'fs';

import { ValidationPipe } from '@nestjs/common';
import { TransformInterceptor } from './common/interceptors/transform.interceptor';
import { HttpExceptionFilter } from './common/interceptors/http.exception.filter';
import { getConfig } from './utils';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
const config = getConfig();
const isDev = config.application.mode === 'dev';
console.log(isDev)
async function bootstrap() {
  const app = await NestFactory.create<NestExpressApplication>(AppModule, {
    logger: ['error', 'warn', 'log', 'debug', 'verbose'],
  });
  app.setGlobalPrefix('api'); //全局前缀
  if (isDev) {
    app.enableCors({
      origin: true, // 或特定域名
      methods: 'GET,HEAD,PUT,PATCH,POST,DELETE',
      credentials: true,
      exposedHeaders: ['Content-Type'],
    }); //允许跨域
    const options = new DocumentBuilder()
      .setTitle('api文档')
      .setDescription('开发环境api文档任务信息管理api')
      .setVersion('1.0')
      .build();
    // 创建文档
    const document = SwaggerModule.createDocument(app, options);
    SwaggerModule.setup('api/document', app, document);
  }

  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true, //如果存在多余属性，是否自动删除
      transform: true, //自动转换类型
      forbidNonWhitelisted: false, //如果存在多余属性，是否报错
      transformOptions: {
        enableImplicitConversion: true,
      },
      // disableErrorMessages:true,
    }),
  );
  app.use(cookieParser());

  app.use(
    session({
      secret: 'shshhsfgfdsdd43fs**^%$%^%##@$%^dfehsh',
      cookie: {
        maxAge: 1000 * 30,
      },
      resave: false,
      saveUninitialized: false,
    }),
  );

  // 接口版本化管理
  // app.enableVersioning({
  //   type: VersioningType.URI,
  // });

  app.useStaticAssets(join(__dirname, '..', 'public'), {
    prefix: '/api/static',
  });

  app.setBaseViewsDir(join(__dirname, '..', 'views'));
  app.setViewEngine('ejs');

  app.useGlobalInterceptors(new TransformInterceptor());
  app.useGlobalFilters(new HttpExceptionFilter());

  app.use((req, res, next) => {
    res.header('Content-Type', 'application/json; charset=utf-8');
    res.setTimeout(30000);
    next();
  });

  // 添加调试中间件，记录请求和响应
  app.use((req, res, next) => {
    const originalSend = res.send;
    
    res.send = function(body) {
      console.log('响应体:', body);
      return originalSend.call(this, body);
    };
    
    next();
  });

  await app.listen(config.application.port);
}
bootstrap();
