import { Button, Image, View } from "@tarojs/components";
import banner from "@/images/login.jpg";
import { getTime } from "@/pages/utils";
import "./index.scss";
import Taro from "@tarojs/taro";

interface Card {
  id: number;
  title: string;
  address: string;
  startTime: string;
  endTime: string;
  time: string;
  // author: string;
  phone: string;
  status: string;
  content: string;
  show: string;
}

const Status = ({item}) => {
  if (item.show == "0") {
    return (
      <View className="flex items-center px-2 py-1 bg-orange-100 rounded-lg">
        <View className="i-ic-baseline-pending-actions mr-1 text-orange-500" />
        <View className="text-orange-500 font-medium">待审核</View>
      </View>
    );
  } else if (item.show == "1") {
   return  <View className="flex items-center px-2 py-1 bg-blue-100 rounded-lg">
      <View className="i-ic-baseline-check-circle mr-1 text-[#1659c0]" />
      <View className="text-[#1659c0] font-medium">审核通过</View>
    </View>;
  } else if (item.show == "-1") {
   return  <View className="flex items-center px-2 py-1 bg-blue-100 rounded-lg">
      <View className="i-ic-baseline-check-circle mr-1 text-[#c01616]" />
      <View className="text-[#c01632] font-medium">审核拒绝</View>
    </View>;
  }
};

export default function Index({
  item,
  isRecommend,
}: {
  item: Card;
  isRecommend?: boolean;
}) {
  return (
    <View
      className="bg-white rounded-xl mx-auto w-[90%] overflow-hidden mb-3 text-left text-sm p-4 relative box-border"
      onClick={() => {
        Taro.redirectTo({
          url: `/pages/part3/detail/index?id=${item.id}&status=${
            item.status
          }&startTime=${item.startTime}&endTime=${item.endTime}&address=${
            item.address
          }&title=${item.title}&phone=${item.phone}&content=${
            item.content
          }&time=${
            getTime({
              startTime: item.startTime,
              endTime: item.endTime,
            }).day
          }`,
        });
      }}
    >
      <View className="bg-[#1659c0] color-white py-1 px-2 absolute left-0 top-0 w-auto rounded-br-xl">
        {item.title}
      </View>
      <View
        style={{ display: isRecommend ? "none" : "block" }}
        className="py-1 px-2 absolute right-0 top-0 w-auto rounded-br-xl color-gray-4"
      >
        <Status item={item} />
      </View>
      <View className="mt-8 mb-3">
        <View className="text-lg font-bold mb-2">
          <View className="i-ic-baseline-location-on inline-block color-gray-4 align-middle text-lg mr-0.5"></View>
          {item.address}
        </View>
        <View className="bg-blue-50 p-3 rounded-lg">
          <View className="text-sm font-medium text-gray-700 mb-1 flex items-center">
            <View className="i-ic-outline-description mr-1 text-[#1659c0]" />
            活动内容
          </View>
          <View className="text-gray-600 text-sm line-clamp-3">
            {item.content}
          </View>
        </View>
      </View>
      <View className=" text-gray-700 bg-slate-100 p-2 mx-auto rounded-md">
        <View className="mb-1.5">
          <View className="i-ic-baseline-calendar-month inline-block text-gray-400 align-sub text-lg mr-1"></View>
          日期：{item.startTime} 至 {item.endTime} 结束
        </View>
        <View className="mb-1.5">
          <View className="i-ic-outline-access-time inline-block text-gray-400 align-sub text-lg mr-1"></View>
          时长：{item.time}天
        </View>
        {/* <View className="mb-1.5">
          <View className="i-ic-sharp-person-outline inline-block text-gray-400 align-sub text-lg mr-1"></View>
          姓名：{item.author}
        </View> */}
        <View>
          <View className="i-iconamoon-squinting-face-light inline-block text-gray-400 align-sub text-lg mr-1"></View>
          联系方式：{item.phone}
        </View>
      </View>
    </View>
  );
}
