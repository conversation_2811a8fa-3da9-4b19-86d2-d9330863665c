import { Injectable, NotFoundException } from '@nestjs/common';
import { CreateBroadcastroomDto } from './dto/create-broadcastroom.dto';
import { UpdateBroadcastroomDto } from './dto/update-broadcastroom.dto';
import { SearchApprovedTaskDto } from './dto/search-task.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { Broadcastroom } from './entities/broadcastroom.entity';
import { Repository, Like, Between } from 'typeorm';
import { Cron, CronExpression } from '@nestjs/schedule';
import { User } from '../user/entities/user.entity';
import { InviteRecord } from '../invite/entities/invite.entity';
import { InviteService } from '../invite/invite.service';
import { Request } from 'express';

@Injectable()
export class BroadcastroomService {
  constructor(
    @InjectRepository(Broadcastroom)
    private readonly broadcastroomRepository: Repository<Broadcastroom>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(InviteRecord)
    private readonly inviteRecordRepository: Repository<InviteRecord>,
    private readonly inviteService: InviteService,
  ) {}

  async create(createBroadcastroomDto: CreateBroadcastroomDto, request: Express.Request) {
    const broadcastroom = await this.broadcastroomRepository.create({
      ...createBroadcastroomDto,
      phone: request.user?.username,
      status: '未结束',
      author: request.user?.id,
    });
    
    const result = await this.broadcastroomRepository.save(broadcastroom);
    
    if (request.user) {
      await this.inviteService.handleTaskCreationReward(result.id, request.user.id);
    }
    
    return result;
  }

  findAll() {
    return this.broadcastroomRepository.find({
      where: {
        status: '未结束',
        show: 1
      },
    });
  }

  @Cron(CronExpression.EVERY_30_MINUTES)
  async checkSchedules() {
    let boardcastroom = await this.broadcastroomRepository.find({
      where: {
        status: '未结束',
      },
    });
    let fall = boardcastroom.filter(
      (item) => new Date() > new Date(item.endTime),
    );
    
    for (let i = 0; i < fall.length; i++) {
      const borads = fall[i];
      await this.broadcastroomRepository.update(
        borads.id,
        {
          status: '已结束',
        },
      );
    }
  }

  async findAllName(request: Express.Request) {
    const broadcastrooms = await this.broadcastroomRepository.findBy({
      author: request.user?.id,
    });
    return broadcastrooms;
  }

  findOne(id: number) {
    return this.broadcastroomRepository.findOne({
      where: {
        id,
      },
    });
  }

  async update(id: number, updateBroadcastroomDto: UpdateBroadcastroomDto) {
    const broadcastroom = await this.broadcastroomRepository.preload({
      id,
      ...updateBroadcastroomDto,
    });
    if (!broadcastroom) {
      throw new NotFoundException(`broadcastroom ${id} 没有找到!`);
    }
    return this.broadcastroomRepository.save(broadcastroom);
  }

  async remove(id: number) {
    const broadcastroom = await this.findOne(id);
    if (!broadcastroom) {
      throw new NotFoundException(`broadcastroom ${id} 没有找到!`);
    }
    return this.broadcastroomRepository.remove(broadcastroom);
  }

  async handleTaskCompletion(taskId: number, userId: number) {
    const basePoints = 100; // 基础分成积分
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['parent']
    });

    // 递归处理上级分成
    let currentUser = user;
    let level = 1;
    while (currentUser.parent && level <= 3) { // 最多往上查3级
      const commission = basePoints * this.getCommissionRate(level);
      await this.userRepository.update(currentUser.parent.id, {
        omn: () => `omn + ${commission}`
      });
      
      // 记录分成奖励
      await this.inviteRecordRepository.save({
        inviterId: currentUser.parent.id,
        inviteeId: userId,
        points: commission,
        rewardType: 'TASK_COMMISSION'
      });

      currentUser = currentUser.parent;
      level++;
    }
  }

  private getCommissionRate(level: number): number {
    // 根据层级返回不同的分成比例
    switch(level) {
      case 1: return 0.05; // 直接上级 5%
      case 2: return 0.03; // 二级上级 3%
      case 3: return 0.02; // 三级上级 2%
      default: return 0;
    }
  }

  async participate(taskId: number, userId: number) {
    // ... existing participation logic ...
    
    // 处理任务参与奖励
    await this.inviteService.handleTaskParticipationReward(taskId, userId);
  }

  /**
   * 搜索已通过的任务
   */
  async searchApprovedTasks(searchDto: SearchApprovedTaskDto) {
    try {
      const {
        keyword,
        address,
        startDate,
        endDate,
        page = 1,
        limit = 10,
        sortBy = 'id',
        sortOrder = 'DESC'
      } = searchDto;

      const skip = (page - 1) * limit;
      
      // 构建查询条件
      const where: any = {
        show: 1, // 只查询已通过的任务
        status: '未结束' // 只查询未结束的任务
      };

      // 添加关键词搜索条件
      if (keyword) {
        where.title = Like(`%${keyword}%`);
      }

      // 添加地址筛选条件
      if (address) {
        where.address = Like(`%${address}%`);
      }

      // 添加时间范围筛选条件
      if (startDate && endDate) {
        where.startTime = Between(startDate, endDate);
      } else if (startDate) {
        where.startTime = `>= ${startDate}`;
      } else if (endDate) {
        where.startTime = `<= ${endDate}`;
      }

      // 执行查询
      const [tasks, total] = await this.broadcastroomRepository.findAndCount({
        where,
        skip,
        take: limit,
        order: { [sortBy]: sortOrder },
        relations: ['users'] // 包含参与用户信息
      });

      return {
        data: tasks.map(task => ({
          ...task,
          participantCount: task.users?.length || 0
        })),
        meta: {
          total,
          page,
          limit,
          totalPages: Math.ceil(total / limit),
          hasNextPage: page < Math.ceil(total / limit),
          hasPrevPage: page > 1
        }
      };
    } catch (error) {
      throw new NotFoundException(`搜索已通过任务失败: ${error.message}`);
    }
  }
}
