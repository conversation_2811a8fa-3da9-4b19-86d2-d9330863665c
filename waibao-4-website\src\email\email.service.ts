import { Injectable, Logger, HttpException, HttpStatus } from '@nestjs/common';
import * as nodemailer from 'nodemailer';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class EmailService {
  private readonly logger = new Logger(EmailService.name);
  private readonly verificationCodes = new Map<string, { code: string; expires: number }>();
  private readonly transporter: nodemailer.Transporter;

  constructor(private readonly configService: ConfigService) {
    // 创建邮件传输器
    this.transporter = nodemailer.createTransport({
      host: this.configService.get('email.smtp.host'),
      port: this.configService.get('email.smtp.port'),
      secure: this.configService.get('email.smtp.secure'),
      auth: {
        user: this.configService.get('email.smtp.user'),
        pass: this.configService.get('email.smtp.pass')
      },
      connectionTimeout: 30000,
      socketTimeout: 30000,
      greetingTimeout: 30000
    });
  }

  async sendVerificationCode(email: string) {
    try {
      this.logger.log(`开始处理发送验证码请求: ${email}`);
      
      const code = Math.random().toString().slice(-6);
      this.logger.log(`生成验证码: ${code} (邮箱: ${email})`);
      
      this.verificationCodes.set(email, {
        code,
        expires: Date.now() + 10 * 60 * 1000
      });
      this.logger.log(`验证码已存储到内存，有效期10分钟 (邮箱: ${email})`);

      // 异步发送邮件
      this.sendEmailAsync(email, code).catch(error => {
        this.logger.error(`异步发送邮件失败: ${email}, 错误: ${error.message}`, error.stack);
      });

      // 立即返回成功状态
      return {
        code: 200,
        data: '验证码已发送',
        success: true,
        message: '操作成功'
      };
    } catch (error) {
      this.logger.error(`验证码处理失败: ${email}, 错误: ${error.message}`, error.stack);
      throw new HttpException('验证码处理失败,请稍后重试', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  private async sendEmailAsync(email: string, code: string): Promise<void> {
    try {
      this.logger.log(`开始发送邮件到: ${email}`);
      const mailResult = await this.transporter.sendMail({
        from: this.configService.get('email.smtp.user'),
        to: email,
        subject: '验证码',
        html: `
          <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
            <h2 style="color: #333; text-align: center;">验证码</h2>
            <div style="background-color: #f5f5f5; padding: 20px; border-radius: 5px; margin: 20px 0;">
              <p style="font-size: 16px; color: #666;">您的验证码是：</p>
              <p style="font-size: 24px; font-weight: bold; color: #333; text-align: center;">${code}</p>
              <p style="color: #666;">验证码有效期为10分钟。</p>
            </div>
            <p style="color: #999; font-size: 14px; text-align: center;">此邮件为系统自动发送，请勿回复</p>
          </div>
        `
      });
      this.logger.log(`邮件发送完成，messageId: ${mailResult.messageId} (邮箱: ${email})`);
    } catch (error) {
      this.logger.error(`邮件发送失败: ${email}, 错误: ${error.message}`, error.stack);
      // 这里我们不抛出异常，因为是异步处理
    }
  }

  verifyCode(email: string, code: string): boolean {
    const stored = this.verificationCodes.get(email);
    if (!stored) {
      return false;
    }

    if (Date.now() > stored.expires) {
      this.verificationCodes.delete(email);
      return false;
    }

    if (stored.code !== code) {
      return false;
    }

    // 验证成功后删除验证码
    this.verificationCodes.delete(email);
    return true;
  }

  async sendResetPasswordEmail(email: string, code: string) {
    try {
      // 先存储验证码
      this.verificationCodes.set(email, {
        code,
        expires: Date.now() + 10 * 60 * 1000
      });
      
      // 异步发送邮件
      this.sendResetPasswordEmailAsync(email, code).catch(error => {
        this.logger.error(`异步发送重置密码邮件失败: ${email}, 错误: ${error.message}`, error.stack);
      });
      
      return true;
    } catch (error) {
      this.logger.error(`重置密码验证码处理失败: ${email}, 错误: ${error.message}`, error.stack);
      return false;
    }
  }

  private async sendResetPasswordEmailAsync(email: string, code: string): Promise<void> {
    try {
      const mailOptions = {
        from: this.configService.get('email.smtp.user'),
        to: email,
        subject: '重置密码验证码',
        html: `
          <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
            <h2 style="color: #333; text-align: center;">重置密码</h2>
            <div style="background-color: #f5f5f5; padding: 20px; border-radius: 5px; margin: 20px 0;">
              <p style="font-size: 16px; color: #666;">您的重置密码验证码是:</p>
              <h1 style="color: #007bff; text-align: center; font-size: 32px; letter-spacing: 5px;">${code}</h1>
              <p style="color: #999; font-size: 14px;">验证码有效期为10分钟</p>
              <p style="color: #666; font-size: 14px;">如果这不是您的操作，请忽略此邮件并确保您的账号安全。</p>
            </div>
            <p style="color: #666; font-size: 14px;">为了保护您的账号安全，请勿将验证码泄露给他人。</p>
          </div>
        `
      };

      const result = await this.transporter.sendMail(mailOptions);
      this.logger.log(`重置密码邮件发送完成，messageId: ${result.messageId} (邮箱: ${email})`);
    } catch (error) {
      this.logger.error(`重置密码邮件发送失败: ${email}, 错误: ${error.message}`, error.stack);
    }
  }

  async sendEmail(to: string, subject: string, html: string) {
    try {
      // 异步发送邮件
      this.sendGeneralEmailAsync(to, subject, html).catch(error => {
        this.logger.error(`异步发送通用邮件失败: ${to}, 错误: ${error.message}`, error.stack);
      });
      
      return true;
    } catch (error) {
      this.logger.error(`通用邮件处理失败: ${to}, 错误: ${error.message}`, error.stack);
      return false;
    }
  }

  private async sendGeneralEmailAsync(to: string, subject: string, html: string): Promise<void> {
    try {
      const mailOptions = {
        from: this.configService.get('email.smtp.user'),
        to,
        subject,
        html
      };
      
      const result = await this.transporter.sendMail(mailOptions);
      this.logger.log(`通用邮件发送完成，messageId: ${result.messageId} (邮箱: ${to})`);
    } catch (error) {
      this.logger.error(`通用邮件发送失败: ${to}, 错误: ${error.message}`, error.stack);
    }
  }
} 