import { View, Form, Input, Button } from "@tarojs/components";
import Taro from "@tarojs/taro";
import { useState, useEffect } from "react";
import VConsole from 'vconsole';

export default function Index() {
  const [error, setError] = useState("");

  useEffect(() => {
    // 只在非生产环境启用 vConsole
    if (process.env.NODE_ENV !== 'production') {
      const vConsole = new VConsole();
      console.log('vConsole 已启用');
      return () => {
        vConsole.destroy();
      };
    }
  }, []);

  const formSubmit = (e) => {
    const username = e.detail.value?.username?.toString();
    const password = e.detail.value?.password;

    if (!username || !password) {
      return Taro.showToast({
        title: "请填写完整！",
        icon: "none",
        duration: 2000,
      });
    }

    console.log('发送登录请求:', {
      url: `${process.env.TARO_APP_HOST}/api/admin/login`,
      username,
      password: '***',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    Taro.request({
      url: `${process.env.TARO_APP_HOST}/api/admin/login`,
      method: "POST",
      header: {
        'Content-Type': 'application/json'
      },
      data: {
        username,
        password,
      },
    })
      .then((res) => {
        console.log('登录响应:', {
          code: res.code,
          data: res.data,
          headers: res.header
        });
        if ((res.data.code === 200 || res.data.code === 201) && res.data.success) {
          console.log("登录响应数据:", res.data);

          if (res.data.data.token) {
            try {
              // 先显示登录成功提示
              Taro.showToast({
                title: "登录成功",
                icon: "success",
                duration: 1500,
              });

              // 保存token
              Taro.setStorageSync("adminToken", res.data.data.token);
              console.log("保存的token:", res.data.data.token);

              // 延时跳转，确保toast显示完毕且token已保存
              setTimeout(() => {
                Taro.navigateTo({
                  url: "/pages/admin/dashboard/index",
                  success: () => {
                    console.log("跳转成功");
                  },
                  fail: (err) => {
                    console.error("navigateTo跳转失败:", err);
                    // 如果navigateTo失败，尝试使用redirectTo
                    Taro.redirectTo({
                      url: "/pages/admin/dashboard/index",
                      fail: (e) => {
                        console.error("redirectTo也失败:", e);
                        // 最后尝试使用switchTab
                        Taro.switchTab({
                          url: "/pages/admin/dashboard/index",
                          fail: (e2) => {
                            console.error("所有跳转方式都失败:", e2);
                            Taro.showToast({
                              title: "页面跳转失败",
                              icon: "none",
                              duration: 2000,
                            });
                          },
                        });
                      },
                    });
                  },
                });
              }, 500);
            } catch (err) {
              console.error("保存token失败:", err);
              Taro.showToast({
                title: "登录失败，请重试",
                icon: "none",
                duration: 2000,
              });
            }
          } else {
            console.error("响应中没有token");
            setError("登录失败，请重试");
          }
        } else {
          setError(res.data.message || "登录失败");
          Taro.showToast({
            title: res.data.message || "登录失败",
            icon: "none",
            duration: 2000,
          });
        }
      })
      .catch((err) => {
        console.error("登录请求失败:", err);
        setError("登录失败");
        Taro.showToast({
          title: "登录失败",
          icon: "none",
          duration: 2000,
        });
      });
  };

  return (
    <Form onSubmit={formSubmit}>
      <View className="full-page flex flex-col">
        <View className="w-full h-full px-10 box-border">
          <View className="text-xl text-center mb-8 mt-20">管理员登录</View>

          <View className="mb-4">
            <Input
              type="text"
              name="username"
              className="w-full py-2 px-4 border rounded"
              placeholder="请输入管理员账号"
            />
          </View>

          <View className="mb-6">
            <Input
              password
              name="password"
              className="w-full py-2 px-4 border rounded"
              placeholder="请输入密码"
            />
          </View>

          {error && (
            <View className="text-red-500 text-center mb-4">{error}</View>
          )}

          <Button
            formType="submit"
            className="bg-[#1659c0] text-white w-full py-2 rounded"
          >
            登录
          </Button>
        </View>
      </View>
    </Form>
  );
}
