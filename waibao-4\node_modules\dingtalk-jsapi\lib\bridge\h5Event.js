"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.off=exports.on=void 0;var NON_BRIDGE_EVENTS=["resume","pause","online","offline","backbutton","goBack","pullToRefresh","message","recycle","restore","drawer","tab","navHelpIcon","navRightButton","navMenu","navTitle","appLinkResponse","internalPageLinkResponse","networkEvent","hostTaskEvent","deviceOrientationChanged","autoCheckIn","deviceFound","hostCheckIn","screenshot","becomeActive","keepAlive","navTitleClick","sharePage","wxNotify","editNoteCommand","updateStyle","qrscanCommonNotify","__message__","dtChannelEvent","livePlayerEventPlay","livePlayerEventPause","livePlayerEventEnded","livePlayerEventError","navActions","attendEvents"],BizEventBridgeType="dtBizBridgeEvent",EventTypeListKey="__eventTypeList__",handlerProxyMap=function(){return"undefined"==typeof WeakMap?void 0:new WeakMap}(),getOnHandlerProxy=function(e,n){if(handlerProxyMap){var t=handlerProxyMap.get(n);return void 0===t?(t=function(e){var a=e.detail;if(a.namespace&&a.eventName){var o="".concat(a.namespace,".").concat(a.eventName);t&&-1!==t[EventTypeListKey].indexOf(o)&&n(a.data)}},t[EventTypeListKey]=[e],handlerProxyMap.set(n,t)):-1===t[EventTypeListKey].indexOf(e)&&t[EventTypeListKey].push(e),t}},getOffHandlerProxy=function(e,n){if(handlerProxyMap){var t=handlerProxyMap.get(n);return t&&-1!==t[EventTypeListKey].indexOf(e)&&t[EventTypeListKey].splice(t[EventTypeListKey].indexOf(e),1),t&&t[EventTypeListKey].length<=1?t:void 0}},on=function(e,n){if(-1!==NON_BRIDGE_EVENTS.indexOf(e))document.addEventListener(e,n);else{var t=getOnHandlerProxy(e,n);t?document.addEventListener(BizEventBridgeType,t):console.log("bind event : ".concat(e," need WeakMap support , current environment doesnot"))}};exports.on=on;var off=function(e,n){if(-1!==NON_BRIDGE_EVENTS.indexOf(e))document.removeEventListener(e,n);else{var t=getOffHandlerProxy(e,n);t&&document.removeEventListener(BizEventBridgeType,t)}};exports.off=off;