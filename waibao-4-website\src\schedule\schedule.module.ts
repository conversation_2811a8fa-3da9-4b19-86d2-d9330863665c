import { Module } from '@nestjs/common';
import { ScheduleService } from './schedule.service';
import { ScheduleController } from './schedule.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Schedule } from './entities/schedule.entity';
import { Broadcastroom } from 'src/broadcastroom/entities/broadcastroom.entity';

@Module({
  controllers: [ScheduleController],
  imports: [
    TypeOrmModule.forFeature([Schedule]),
    TypeOrmModule.forFeature([Broadcastroom])],
  providers: [ScheduleService]
})
export class Schedule2Module {}
