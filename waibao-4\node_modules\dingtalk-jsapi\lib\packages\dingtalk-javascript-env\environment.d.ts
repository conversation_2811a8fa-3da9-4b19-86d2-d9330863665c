export default function environment(runtime: any, framework: any, virtualEnv: any): {
    isDingTalk: boolean;
    isWebiOS: boolean;
    isWebAndroid: boolean;
    isWeexiOS: boolean;
    isWeexAndroid: boolean;
    isDingTalkPCMac: boolean;
    isDingTalkPCWeb: boolean;
    isDingTalkPCWindows: boolean;
    isDingTalkPC: boolean;
    runtime: any;
    framework: any;
    platform: string;
    version: any;
    isWeex: boolean;
};
