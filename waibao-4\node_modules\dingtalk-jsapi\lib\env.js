"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.getENV=exports.getUA=exports.ENV_ENUM_SUB=exports.APP_TYPE=exports.ENV_ENUM=void 0;var sdk_1=require("./sdk"),sdk_2=require("./sdk");Object.defineProperty(exports,"ENV_ENUM",{enumerable:!0,get:function(){return sdk_2.ENV_ENUM}}),Object.defineProperty(exports,"APP_TYPE",{enumerable:!0,get:function(){return sdk_2.APP_TYPE}}),Object.defineProperty(exports,"ENV_ENUM_SUB",{enumerable:!0,get:function(){return sdk_2.ENV_ENUM_SUB}});var dingtalk_javascript_env_1=require("./packages/dingtalk-javascript-env"),getTopBridge=function(){try{if("undefined"!=typeof window&&void 0!==window.top){return window.top.__dingtalk_jsapi_top_platfrom_config__}}catch(e){return}},EDdWeexEnv;!function(e){e.singlePage="singlePage",e.miniApp="miniApp",e.miniWidget="miniWidget"}(EDdWeexEnv||(EDdWeexEnv={}));var getUA=function(){var e="";try{"undefined"!=typeof navigator&&(e=navigator&&(navigator.userAgent||navigator.swuserAgent)||"")}catch(t){e=""}return e};exports.getUA=getUA;var getENV=function(){var e=(0,exports.getUA)(),t=/iPhone|iPad|iPod|iOS/i.test(e),n=/Android/i.test(e),i=(/Nebula/i.test(e),/DingTalk/i.test(e)),a=/dd-web/i.test(e),r="object"==typeof nuva,d="object"==typeof dd&&"function"==typeof dd.dtBridge,o=/OpenHarmony/i.test(e)&&/ArkWeb/i.test(e),_=d&&t||r&&t,s=i||dingtalk_javascript_env_1.default.isDingTalk,E=t&&s||dingtalk_javascript_env_1.default.isWeexiOS||_,p=n&&s||dingtalk_javascript_env_1.default.isWeexAndroid,g=d,c=a,v=o&&s,u=sdk_1.APP_TYPE.WEB;if(c)u=sdk_1.APP_TYPE.WEBVIEW_IN_MINIAPP;else if(g)u=sdk_1.APP_TYPE.MINI_APP;else if(dingtalk_javascript_env_1.default.isWeexiOS||dingtalk_javascript_env_1.default.isWeexAndroid)try{var f=weex.config.ddWeexEnv;u=f===EDdWeexEnv.miniWidget?sdk_1.APP_TYPE.WEEX_WIDGET:sdk_1.APP_TYPE.WEEX}catch(e){u=sdk_1.APP_TYPE.WEEX}var l,P="*",N=e.match(/AliApp\(\w+\/([a-zA-Z0-9.-]+)\)/);null===N&&(N=e.match(/DingTalk\/([a-zA-Z0-9.-]+)/));var k;N&&N[1]&&(k=N[1]);var A="";"undefined"!=typeof name&&(A=name);var U=getTopBridge();try{U&&"undefined"!=typeof window&&void 0!==window.top&&window.top!==window&&(A=top.name)}catch(e){}if(A)try{var x=JSON.parse(A);x.hostVersion&&(k=x.hostVersion),P=x.language||navigator.language||"*",l=x.containerId}catch(e){}var m=!!l;m&&!k&&(N=e.match(/DingTalk\(([a-zA-Z0-9\.-]+)\)/))&&N[1]&&(k=N[1]);var V,M=sdk_1.ENV_ENUM_SUB.noSub;if((V=E?sdk_1.ENV_ENUM.ios:p&&!v?sdk_1.ENV_ENUM.android:v?sdk_1.ENV_ENUM.harmony:m?sdk_1.ENV_ENUM.pc:U&&U.platform?U.platform:sdk_1.ENV_ENUM.notInDingTalk)===sdk_1.ENV_ENUM.pc){M=e.indexOf("Macintosh; Intel Mac OS")>-1?sdk_1.ENV_ENUM_SUB.mac:sdk_1.ENV_ENUM_SUB.win}return{platform:V,platformSub:M,version:k,appType:u,language:P}};exports.getENV=getENV;